stm32h7xx_hal.c:228:13:<PERSON><PERSON>_MspInit	0	static
stm32h7xx_hal.c:239:13:H<PERSON>_MspDeInit	0	static
stm32h7xx_hal.c:187:19:HAL_DeInit	8	static
stm32h7xx_hal.c:262:26:H<PERSON>_InitTick	16	static
stm32h7xx_hal.c:134:19:HAL_Init	16	static
stm32h7xx_hal.c:327:13:H<PERSON>_IncTick	0	static
stm32h7xx_hal.c:338:17:<PERSON>AL_GetTick	0	static
stm32h7xx_hal.c:347:10:HAL_GetTickPrio	0	static
stm32h7xx_hal.c:356:19:HAL_SetTickFreq	16	static
stm32h7xx_hal.c:389:21:HAL_GetTickFreq	0	static
stm32h7xx_hal.c:405:13:<PERSON><PERSON>_<PERSON><PERSON>	16	static
stm32h7xx_hal.c:431:13:H<PERSON>_SuspendTick	0	static
stm32h7xx_hal.c:447:13:HAL_ResumeTick	0	static
stm32h7xx_hal.c:457:10:HAL_GetHalVersion	0	static
stm32h7xx_hal.c:466:10:HAL_GetREVID	0	static
stm32h7xx_hal.c:475:10:HAL_GetDEVID	0	static
stm32h7xx_hal.c:484:10:HAL_GetUIDw0	0	static
stm32h7xx_hal.c:493:10:HAL_GetUIDw1	0	static
stm32h7xx_hal.c:502:10:HAL_GetUIDw2	0	static
stm32h7xx_hal.c:521:6:HAL_SYSCFG_VREFBUF_VoltageScalingConfig	0	static
stm32h7xx_hal.c:537:6:HAL_SYSCFG_VREFBUF_HighImpedanceConfig	0	static
stm32h7xx_hal.c:549:6:HAL_SYSCFG_VREFBUF_TrimmingConfig	0	static
stm32h7xx_hal.c:561:19:HAL_SYSCFG_EnableVREFBUF	16	static
stm32h7xx_hal.c:587:6:HAL_SYSCFG_DisableVREFBUF	0	static
stm32h7xx_hal.c:601:6:HAL_SYSCFG_ETHInterfaceSelect	0	static
stm32h7xx_hal.c:631:6:HAL_SYSCFG_AnalogSwitchConfig	0	static
stm32h7xx_hal.c:649:6:HAL_SYSCFG_EnableBOOST	0	static
stm32h7xx_hal.c:661:6:HAL_SYSCFG_DisableBOOST	0	static
stm32h7xx_hal.c:677:6:HAL_SYSCFG_CM7BootAddConfig	0	static
stm32h7xx_hal.c:713:6:HAL_SYSCFG_CM4BootAddConfig	0	static
stm32h7xx_hal.c:736:6:HAL_SYSCFG_EnableCM7BOOT	0	static
stm32h7xx_hal.c:746:6:HAL_SYSCFG_DisableCM7BOOT	0	static
stm32h7xx_hal.c:755:6:HAL_SYSCFG_EnableCM4BOOT	0	static
stm32h7xx_hal.c:765:6:HAL_SYSCFG_DisableCM4BOOT	0	static
stm32h7xx_hal.c:776:6:HAL_EnableCompensationCell	0	static
stm32h7xx_hal.c:787:6:HAL_DisableCompensationCell	0	static
stm32h7xx_hal.c:800:6:HAL_SYSCFG_EnableIOSpeedOptimize	0	static
stm32h7xx_hal.c:816:6:HAL_SYSCFG_DisableIOSpeedOptimize	0	static
stm32h7xx_hal.c:833:6:HAL_SYSCFG_CompensationCodeSelect	0	static
stm32h7xx_hal.c:850:6:HAL_SYSCFG_CompensationCodeConfig	0	static
stm32h7xx_hal.c:912:6:HAL_DBGMCU_EnableDBGSleepMode	0	static
stm32h7xx_hal.c:921:6:HAL_DBGMCU_DisableDBGSleepMode	0	static
stm32h7xx_hal.c:931:6:HAL_DBGMCU_EnableDBGStopMode	0	static
stm32h7xx_hal.c:940:6:HAL_DBGMCU_DisableDBGStopMode	0	static
stm32h7xx_hal.c:949:6:HAL_DBGMCU_EnableDBGStandbyMode	0	static
stm32h7xx_hal.c:958:6:HAL_DBGMCU_DisableDBGStandbyMode	0	static
stm32h7xx_hal.c:968:6:HAL_EnableDomain2DBGSleepMode	0	static
stm32h7xx_hal.c:977:6:HAL_DisableDomain2DBGSleepMode	0	static
stm32h7xx_hal.c:986:6:HAL_EnableDomain2DBGStopMode	0	static
stm32h7xx_hal.c:995:6:HAL_DisableDomain2DBGStopMode	0	static
stm32h7xx_hal.c:1004:6:HAL_EnableDomain2DBGStandbyMode	0	static
stm32h7xx_hal.c:1013:6:HAL_DisableDomain2DBGStandbyMode	0	static
stm32h7xx_hal.c:1065:6:HAL_SetFMCMemorySwappingConfig	0	static
stm32h7xx_hal.c:1077:10:HAL_GetFMCMemorySwappingConfig	0	static
stm32h7xx_hal.c:1094:6:HAL_EXTI_EdgeConfig	8	static
stm32h7xx_hal.c:1120:6:HAL_EXTI_GenerateSWInterrupt	0	static
stm32h7xx_hal.c:1135:6:HAL_EXTI_D1_ClearFlag	0	static
stm32h7xx_hal.c:1150:6:HAL_EXTI_D2_ClearFlag	0	static
stm32h7xx_hal.c:1170:6:HAL_EXTI_D1_EventInputConfig	12	static
stm32h7xx_hal.c:1216:6:HAL_EXTI_D2_EventInputConfig	12	static
stm32h7xx_hal.c:1264:6:HAL_EXTI_D3_EventInputConfig	12	static
