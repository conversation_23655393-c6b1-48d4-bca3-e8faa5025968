# 完整TouchGFX项目编译脚本
# 使用STM32CubeCLT工具链编译包含ADC控制仪表盘功能的完整TouchGFX项目

Write-Host "开始编译完整TouchGFX项目..." -ForegroundColor Green

# 设置工具链路径
$TOOLCHAIN_PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin"
$GCC = "$TOOLCHAIN_PATH\arm-none-eabi-gcc.exe"
$OBJCOPY = "$TOOLCHAIN_PATH\arm-none-eabi-objcopy.exe"
$SIZE = "$TOOLCHAIN_PATH\arm-none-eabi-size.exe"

# 编译器标志
$CFLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb",
    "-mfpu=fpv5-d16",
    "-mfloat-abi=hard",
    "-DUSE_HAL_DRIVER",
    "-DSTM32H745xx",
    "-DCORE_CM7",
    "-DUSE_FULL_LL_DRIVER",
    "-DDATA_IN_D2_SRAM",
    "-O2",
    "-Wall",
    "-fdata-sections",
    "-ffunction-sections",
    "-g",
    "-gdwarf-2"
)

# 包含路径
$INCLUDES = @(
    "-ICM7/Core/Inc",
    "-ICM7/TouchGFX/App",
    "-ICM7/TouchGFX/target/generated",
    "-ICM7/TouchGFX/target",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/include",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1",
    "-IDrivers/CMSIS/Device/ST/STM32H7xx/Include",
    "-IDrivers/CMSIS/Include",
    "-IMiddlewares/ST/touchgfx/framework/include",
    "-ICM7/TouchGFX/generated/fonts/include",
    "-ICM7/TouchGFX/generated/gui_generated/include",
    "-ICM7/TouchGFX/generated/images/include",
    "-ICM7/TouchGFX/generated/texts/include",
    "-ICM7/TouchGFX/gui/include",
    "-IDrivers/BSP/STM32H745I-DISCO",
    "-IDrivers/BSP/Components/Common",
    "-IDrivers/BSP/Components/otm8009a",
    "-IDrivers/BSP/Components/ft5336"
)

# 源文件列表
$SOURCES = @(
    "CM7/Core/Src/main.c",
    "CM7/Core/Src/stm32h7xx_it.c",
    "CM7/Core/Src/stm32h7xx_hal_msp.c",
    "CM7/Core/Src/stm32h7xx_hal_timebase_tim.c",
    "CM7/Core/Src/system_stm32h7xx.c",
    "CM7/Core/Src/sysmem.c",
    "CM7/Core/Src/syscalls.c",
    "CM7/TouchGFX/App/app_touchgfx.c",
    "CM7/TouchGFX/target/generated/TouchGFXConfiguration.cpp",
    "CM7/TouchGFX/target/generated/TouchGFXGeneratedHAL.cpp",
    "CM7/TouchGFX/target/STM32TouchController.cpp",
    "CM7/TouchGFX/target/TouchGFXHAL.cpp",
    "CM7/TouchGFX/generated/gui_generated/src/common/FrontendApplicationBase.cpp",
    "CM7/TouchGFX/generated/gui_generated/src/screen1_screen/Screen1ViewBase.cpp",
    "CM7/TouchGFX/generated/gui_generated/src/screen1_screen/Screen1PresenterBase.cpp",
    "CM7/TouchGFX/gui/src/common/FrontendApplication.cpp",
    "CM7/TouchGFX/gui/src/model/Model.cpp",
    "CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp",
    "CM7/TouchGFX/gui/src/screen1_screen/Screen1Presenter.cpp",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dsi.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_jpeg.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.c",
    "Middlewares/Third_Party/FreeRTOS/Source/croutine.c",
    "Middlewares/Third_Party/FreeRTOS/Source/event_groups.c",
    "Middlewares/Third_Party/FreeRTOS/Source/list.c",
    "Middlewares/Third_Party/FreeRTOS/Source/queue.c",
    "Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c",
    "Middlewares/Third_Party/FreeRTOS/Source/tasks.c",
    "Middlewares/Third_Party/FreeRTOS/Source/timers.c",
    "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c",
    "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1/port.c",
    "Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c",
    "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery.c",
    "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_lcd.c",
    "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_sdram.c",
    "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_ts.c",
    "Drivers/BSP/Components/otm8009a/otm8009a.c",
    "Drivers/BSP/Components/ft5336/ft5336.c"
)

# 汇编文件
$ASM_SOURCES = @(
    "CM7/Core/Startup/startup_stm32h745zitx.s"
)

# 链接脚本
$LDSCRIPT = "CM7/STM32H745ZITX_FLASH.ld"

# TouchGFX预编译库
$TOUCHGFX_LIB = "Middlewares/ST/touchgfx/lib/core/cortex_m7/gcc/libtouchgfx-float-abi-hard.a"

# 链接器标志
$LDFLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb",
    "-mfpu=fpv5-d16",
    "-mfloat-abi=hard",
    "-specs=nano.specs",
    "-T$LDSCRIPT",
    "-lc",
    "-lm",
    "-lnosys",
    "-Wl,-Map=build/output.map,--cref",
    "-Wl,--gc-sections",
    "-static",
    "--specs=rdimon.specs"
)

# 创建构建目录
if (!(Test-Path "build")) {
    New-Item -ItemType Directory -Path "build"
}

Write-Host "编译源文件..." -ForegroundColor Yellow

# 编译所有C/C++源文件
$OBJECTS = @()
foreach ($source in $SOURCES) {
    if (Test-Path $source) {
        $obj_name = "build/" + [System.IO.Path]::GetFileNameWithoutExtension($source) + ".o"
        $OBJECTS += $obj_name
        
        Write-Host "编译: $source" -ForegroundColor Cyan
        $compile_cmd = @($GCC) + $CFLAGS + $INCLUDES + @("-c", $source, "-o", $obj_name)
        & $compile_cmd[0] $compile_cmd[1..($compile_cmd.Length-1)]
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "编译失败: $source" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "警告: 源文件不存在: $source" -ForegroundColor Yellow
    }
}

# 编译汇编文件
foreach ($asm_source in $ASM_SOURCES) {
    if (Test-Path $asm_source) {
        $obj_name = "build/" + [System.IO.Path]::GetFileNameWithoutExtension($asm_source) + ".o"
        $OBJECTS += $obj_name
        
        Write-Host "编译汇编: $asm_source" -ForegroundColor Cyan
        $asm_cmd = @($GCC) + $CFLAGS + @("-c", $asm_source, "-o", $obj_name)
        & $asm_cmd[0] $asm_cmd[1..($asm_cmd.Length-1)]
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "汇编编译失败: $asm_source" -ForegroundColor Red
            exit 1
        }
    }
}

Write-Host "链接..." -ForegroundColor Yellow

# 链接生成ELF文件
$link_cmd = @($GCC) + $OBJECTS + @($TOUCHGFX_LIB) + $LDFLAGS + @("-o", "build/TouchGFX_Complete.elf")
& $link_cmd[0] $link_cmd[1..($link_cmd.Length-1)]

if ($LASTEXITCODE -ne 0) {
    Write-Host "链接失败!" -ForegroundColor Red
    exit 1
}

Write-Host "生成HEX文件..." -ForegroundColor Yellow

# 生成HEX文件
& $OBJCOPY -O ihex "build/TouchGFX_Complete.elf" "build/TouchGFX_Complete.hex"

if ($LASTEXITCODE -ne 0) {
    Write-Host "HEX文件生成失败!" -ForegroundColor Red
    exit 1
}

# 显示大小信息
Write-Host "程序大小信息:" -ForegroundColor Green
& $SIZE "build/TouchGFX_Complete.elf"

Write-Host "编译完成! 输出文件:" -ForegroundColor Green
Write-Host "  ELF: build/TouchGFX_Complete.elf" -ForegroundColor White
Write-Host "  HEX: build/TouchGFX_Complete.hex" -ForegroundColor White

# 询问是否下载到开发板
$download = Read-Host "是否下载到STM32H745I-DISCO开发板? (y/n)"
if ($download -eq "y" -or $download -eq "Y") {
    Write-Host "下载固件到开发板..." -ForegroundColor Yellow
    
    $PROGRAMMER = "C:\ST\STM32CubeCLT_1.18.0\STM32CubeProgrammer\bin\STM32_Programmer_CLI.exe"
    & $PROGRAMMER -c port=SWD -w "build/TouchGFX_Complete.hex" 0x08000000 -v -rst
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "固件下载成功!" -ForegroundColor Green
        Write-Host "开发板应该显示TouchGFX界面，ADC1(PA1)控制右侧仪表盘" -ForegroundColor Cyan
    } else {
        Write-Host "固件下载失败!" -ForegroundColor Red
    }
}
