@echo off
echo Compiling missing TouchGFX assets...

REM Set toolchain path
set TOOLCHAIN_PATH=C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin
set GCC=%TOOLCHAIN_PATH%\arm-none-eabi-gcc.exe

echo Using toolchain: %TOOLCHAIN_PATH%

REM Compile missing image assets
echo Compiling image assets...
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/Middlewares/ST/touchgfx/framework/include -ICM7/TouchGFX/generated/images/include -c "CM7/TouchGFX/generated/images/src/image_VC1.cpp" -o "build_touchgfx_complete/image_VC1.o"
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/Middlewares/ST/touchgfx/framework/include -ICM7/TouchGFX/generated/images/include -c "CM7/TouchGFX/generated/images/src/image_VC2.cpp" -o "build_touchgfx_complete/image_VC2.o"
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/Middlewares/ST/touchgfx/framework/include -ICM7/TouchGFX/generated/images/include -c "CM7/TouchGFX/generated/images/src/image_VC3.cpp" -o "build_touchgfx_complete/image_VC3.o"
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/Middlewares/ST/touchgfx/framework/include -ICM7/TouchGFX/generated/images/include -c "CM7/TouchGFX/generated/images/src/image_VC4.cpp" -o "build_touchgfx_complete/image_VC4.o"
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/Middlewares/ST/touchgfx/framework/include -ICM7/TouchGFX/generated/images/include -c "CM7/TouchGFX/generated/images/src/image_VC5.cpp" -o "build_touchgfx_complete/image_VC5.o"
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/Middlewares/ST/touchgfx/framework/include -ICM7/TouchGFX/generated/images/include -c "CM7/TouchGFX/generated/images/src/image_VC6.cpp" -o "build_touchgfx_complete/image_VC6.o"
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/Middlewares/ST/touchgfx/framework/include -ICM7/TouchGFX/generated/images/include -c "CM7/TouchGFX/generated/images/src/image_VC7.cpp" -o "build_touchgfx_complete/image_VC7.o"
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/Middlewares/ST/touchgfx/framework/include -ICM7/TouchGFX/generated/images/include -c "CM7/TouchGFX/generated/images/src/image_VC8.cpp" -o "build_touchgfx_complete/image_VC8.o"

REM Compile BitmapDatabase
echo Compiling BitmapDatabase...
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/TouchGFX/generated/images/include -ICM7/Middlewares/ST/touchgfx/framework/include -c "CM7/TouchGFX/generated/images/src/BitmapDatabase.cpp" -o "build_touchgfx_complete/BitmapDatabase_new.o"

REM Compile font tables with kerning
echo Compiling font tables...
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/TouchGFX/generated/fonts/include -ICM7/Middlewares/ST/touchgfx/framework/include -c "CM7/TouchGFX/generated/fonts/src/Table_verdana_10_4bpp.cpp" -o "build_touchgfx_complete/Table_verdana_10_4bpp_new.o"
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/TouchGFX/generated/fonts/include -ICM7/Middlewares/ST/touchgfx/framework/include -c "CM7/TouchGFX/generated/fonts/src/Table_verdana_20_4bpp.cpp" -o "build_touchgfx_complete/Table_verdana_20_4bpp_new.o"
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/TouchGFX/generated/fonts/include -ICM7/Middlewares/ST/touchgfx/framework/include -c "CM7/TouchGFX/generated/fonts/src/Table_verdana_40_4bpp.cpp" -o "build_touchgfx_complete/Table_verdana_40_4bpp_new.o"

if %ERRORLEVEL% equ 0 (
    echo All missing assets compiled successfully!
    echo Ready for final linking...
) else (
    echo Asset compilation failed!
    pause
    exit /b 1
)

pause
