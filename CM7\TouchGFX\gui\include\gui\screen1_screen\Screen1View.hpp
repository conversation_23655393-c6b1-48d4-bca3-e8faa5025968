#ifndef SCREEN1VIEW_HPP
#define SCREEN1VIEW_HPP

#include <gui_generated/screen1_screen/Screen1ViewBase.hpp>

class Screen1Presenter;

class Screen1View : public Screen1ViewBase
{
public:
    Screen1View();
    virtual ~Screen1View() {}
    virtual void setupScreen();
    virtual void tearDownScreen();

    // 仪表盘更新方法 - 由Presenter调用
    void updateGauge2(uint8_t value);

protected:
};

#endif // SCREEN1VIEW_HPP
