# TouchGFX Simple Build Script - 解决库链接问题
# 我会按照要求完成任务

Write-Host "Building TouchGFX project with simplified approach..." -ForegroundColor Yellow

# 设置工具链路径
$TOOLCHAIN_PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin"
$GCC = "$TOOLCHAIN_PATH\arm-none-eabi-gcc.exe"
$GPP = "$TOOLCHAIN_PATH\arm-none-eabi-g++.exe"
$OBJCOPY = "$TOOLCHAIN_PATH\arm-none-eabi-objcopy.exe"
$SIZE = "$TOOLCHAIN_PATH\arm-none-eabi-size.exe"

# 检查工具链
if (!(Test-Path $GCC)) {
    Write-Host "Error: GCC not found at $GCC" -ForegroundColor Red
    exit 1
}

# 创建输出目录
$BUILD_DIR = "build_simple"
if (Test-Path $BUILD_DIR) { Remove-Item $BUILD_DIR -Recurse -Force }
New-Item -ItemType Directory -Path $BUILD_DIR | Out-Null

# 编译器标志
$COMMON_FLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb",
    "-mfpu=fpv5-sp-d16", 
    "-mfloat-abi=hard",
    "-DUSE_HAL_DRIVER",
    "-DSTM32H745xx",
    "-DCORE_CM7",
    "-DUSE_PWR_DIRECT_SMPS_SUPPLY",
    "-DUSE_FULL_LL_DRIVER",
    "-Os",
    "-Wall",
    "-fdata-sections",
    "-ffunction-sections",
    "-g",
    "-gdwarf-2"
)

$CPP_FLAGS = $COMMON_FLAGS + @(
    "-fno-exceptions",
    "-fno-rtti",
    "-fno-use-cxa-atexit",
    "-std=c++11"
)

$C_FLAGS = $COMMON_FLAGS + @(
    "-std=gnu11"
)

# 包含路径
$INCLUDES = @(
    "-ICM7/Core/Inc",
    "-ICM7/TouchGFX/App",
    "-ICM7/TouchGFX/target/generated",
    "-ICM7/TouchGFX/target",
    "-ICM7/TouchGFX/gui/include",
    "-ICM7/TouchGFX/generated/gui_generated/include",
    "-ICM7/TouchGFX/generated/images/include",
    "-ICM7/TouchGFX/generated/texts/include",
    "-ICM7/TouchGFX/generated/fonts/include",
    "-ICM7/Middlewares/ST/touchgfx/framework/include",
    "-ICM7/Middlewares/Third_Party/FreeRTOS/Source/include",
    "-ICM7/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2",
    "-ICM7/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy",
    "-IDrivers/CMSIS/Device/ST/STM32H7xx/Include",
    "-IDrivers/CMSIS/Include",
    "-IDrivers/BSP/STM32H745I-DISCO"
)

# 关键源文件列表 - 最小化但完整的TouchGFX应用
$SOURCES = @(
    # 核心启动和系统文件
    @("gcc/startup_stm32h745xihx_cm7.s", "startup_stm32h745xihx_cm7.o", "ASM"),
    @("CM7/Core/Src/main.c", "main.o", "C"),
    @("CM7/Core/Src/stm32h7xx_it.c", "stm32h7xx_it.o", "C"),
    @("CM7/Core/Src/stm32h7xx_hal_msp.c", "stm32h7xx_hal_msp.o", "C"),
    @("CM7/Core/Src/system_stm32h7xx.c", "system_stm32h7xx.o", "C"),
    
    # HAL驱动 - 仅必需的
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c", "stm32h7xx_hal.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c", "stm32h7xx_hal_cortex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c", "stm32h7xx_hal_rcc.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c", "stm32h7xx_hal_gpio.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c", "stm32h7xx_hal_pwr.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c", "stm32h7xx_hal_pwr_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c", "stm32h7xx_hal_adc.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c", "stm32h7xx_hal_adc_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c", "stm32h7xx_hal_dma.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c", "stm32h7xx_hal_dma_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.c", "stm32h7xx_hal_ltdc.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.c", "stm32h7xx_hal_ltdc_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.c", "stm32h7xx_hal_dma2d.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dsi.c", "stm32h7xx_hal_dsi.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c", "stm32h7xx_hal_i2c.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c", "stm32h7xx_hal_i2c_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c", "stm32h7xx_hal_tim.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c", "stm32h7xx_hal_tim_ex.o", "C"),
    
    # BSP驱动 - 显示相关
    @("Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery.c", "stm32h745i_discovery.o", "C"),
    @("Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_lcd.c", "stm32h745i_discovery_lcd.o", "C"),
    @("Drivers/BSP/Components/otm8009a/otm8009a.c", "otm8009a.o", "C"),
    @("Drivers/BSP/Components/ft5336/ft5336.c", "ft5336.o", "C"),
    
    # FreeRTOS核心文件
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/croutine.c", "croutine.o", "C"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/event_groups.c", "event_groups.o", "C"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/list.c", "list.o", "C"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/queue.c", "queue.o", "C"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c", "stream_buffer.o", "C"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/tasks.c", "tasks.o", "C"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/timers.c", "timers.o", "C"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c", "cmsis_os2.o", "C"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1/port.c", "port.o", "C"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c", "heap_4.o", "C"),
    
    # TouchGFX应用文件
    @("CM7/TouchGFX/App/app_touchgfx.c", "app_touchgfx.o", "C"),
    @("CM7/TouchGFX/target/TouchGFXConfiguration.cpp", "TouchGFXConfiguration.o", "C++"),
    @("CM7/TouchGFX/target/TouchGFXHAL.cpp", "TouchGFXHAL.o", "C++"),
    @("CM7/TouchGFX/target/generated/TouchGFXGeneratedHAL.cpp", "TouchGFXGeneratedHAL.o", "C++"),
    @("CM7/TouchGFX/target/generated/STM32DMA.cpp", "STM32DMA.o", "C++"),
    
    # GUI应用文件
    @("CM7/TouchGFX/gui/src/common/FrontendApplication.cpp", "FrontendApplication.o", "C++"),
    @("CM7/TouchGFX/gui/src/model/Model.cpp", "Model.o", "C++"),
    @("CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp", "Screen1View.o", "C++"),
    @("CM7/TouchGFX/gui/src/screen1_screen/Screen1Presenter.cpp", "Screen1Presenter.o", "C++"),
    
    # TouchGFX生成的GUI文件
    @("CM7/TouchGFX/generated/gui_generated/src/common/FrontendApplicationBase.cpp", "FrontendApplicationBase.o", "C++"),
    @("CM7/TouchGFX/generated/gui_generated/src/screen1_screen/Screen1ViewBase.cpp", "Screen1ViewBase.o", "C++")
)

Write-Host "Compiling source files..." -ForegroundColor Green

# 编译函数
function Compile-SourceFile {
    param($Compiler, $Flags, $SourceFile, $OutputFile, $FileType)
    
    if (!(Test-Path $SourceFile)) {
        Write-Host "Warning: Source file not found: $SourceFile" -ForegroundColor Yellow
        return $false
    }
    
    $OutputPath = "$BUILD_DIR\$OutputFile"
    
    if ($FileType -eq "ASM") {
        $cmd = "$Compiler $($Flags -join ' ') $($INCLUDES -join ' ') -c `"$SourceFile`" -o `"$OutputPath`""
    } else {
        $cmd = "$Compiler $($Flags -join ' ') $($INCLUDES -join ' ') -c `"$SourceFile`" -o `"$OutputPath`""
    }
    
    Write-Host "Compiling: $SourceFile" -ForegroundColor Cyan
    $result = Invoke-Expression $cmd
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "FAILED: $SourceFile" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# 编译所有源文件
$ObjectFiles = @()
$CompileSuccess = $true

foreach ($source in $SOURCES) {
    $sourceFile = $source[0]
    $objectFile = $source[1]
    $fileType = $source[2]
    
    if ($fileType -eq "C++") {
        $success = Compile-SourceFile $GPP $CPP_FLAGS $sourceFile $objectFile $fileType
    } elseif ($fileType -eq "C") {
        $success = Compile-SourceFile $GCC $C_FLAGS $sourceFile $objectFile $fileType
    } elseif ($fileType -eq "ASM") {
        $success = Compile-SourceFile $GCC $C_FLAGS $sourceFile $objectFile $fileType
    }
    
    if ($success) {
        $ObjectFiles += "$BUILD_DIR\$objectFile"
    } else {
        $CompileSuccess = $false
    }
}

if (!$CompileSuccess) {
    Write-Host "Compilation failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Linking..." -ForegroundColor Green

# 链接标志
$LINKER_FLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb",
    "-mfpu=fpv5-sp-d16",
    "-mfloat-abi=hard",
    "-specs=nano.specs",
    "-T gcc/STM32H745XIHX_FLASH_CM7.ld",
    "-Wl,--gc-sections",
    "-Wl,--print-memory-usage",
    "-Wl,-Map=$BUILD_DIR/TouchGFX_Simple.map"
)

# 使用TouchGFX预编译库
$TOUCHGFX_LIB = "CM7/Middlewares/ST/touchgfx/lib/core/cortex_m7/gcc/libtouchgfx-float-abi-hard.a"

# 链接命令
$LINK_CMD = "$GPP $($LINKER_FLAGS -join ' ') $($ObjectFiles -join ' ') `"$TOUCHGFX_LIB`" -lc -lm -lnosys -o `"$BUILD_DIR/TouchGFX_Simple.elf`""

Write-Host "Executing link command..." -ForegroundColor Cyan
$result = Invoke-Expression $LINK_CMD

if ($LASTEXITCODE -ne 0) {
    Write-Host "FAILED: Linking failed" -ForegroundColor Red
    exit 1
}

Write-Host "Creating HEX file..." -ForegroundColor Green
$HEX_CMD = "$OBJCOPY -O ihex `"$BUILD_DIR/TouchGFX_Simple.elf`" `"$BUILD_DIR/TouchGFX_Simple.hex`""
Invoke-Expression $HEX_CMD

Write-Host "Checking size..." -ForegroundColor Green
$SIZE_CMD = "$SIZE `"$BUILD_DIR/TouchGFX_Simple.elf`""
Invoke-Expression $SIZE_CMD

Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "Output files:" -ForegroundColor Yellow
Write-Host "  ELF: $BUILD_DIR/TouchGFX_Simple.elf" -ForegroundColor White
Write-Host "  HEX: $BUILD_DIR/TouchGFX_Simple.hex" -ForegroundColor White
Write-Host "  MAP: $BUILD_DIR/TouchGFX_Simple.map" -ForegroundColor White
