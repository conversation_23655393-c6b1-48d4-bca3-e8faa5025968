stm32h7xx_ll_adc.h:5409:22:LL_<PERSON><PERSON>_SetChannelSamplingTime	12	static
stm32h7xx_ll_adc.h:5774:22:LL_ADC_SetAnalogWDMonitChannels	12	static
stm32h7xx_ll_adc.h:6941:26:LL_ADC_IsEnabled	0	static
stm32h7xx_ll_adc.h:7073:26:LL_ADC_REG_IsConversionOngoing	0	static
stm32h7xx_hal_adc.c:1095:13:HAL_ADC_MspInit	0	static
stm32h7xx_hal_adc.c:1112:13:HAL_ADC_MspDeInit	0	static
stm32h7xx_hal_adc.c:1572:19:HAL_ADC_PollForConversion	24	static
stm32h7xx_hal_adc.c:1735:19:HAL_ADC_PollForEvent	24	static
stm32h7xx_hal_adc.c:2344:10:H<PERSON>_ADC_GetValue	0	static
stm32h7xx_hal_adc.c:2721:13:HAL_ADC_ConvCpltCallback	0	static
stm32h7xx_hal_adc.c:2736:13:HAL_ADC_ConvHalfCpltCallback	0	static
stm32h7xx_hal_adc.c:3894:6:ADC_DMAHalfConvCplt	8	static
stm32h7xx_hal_adc.c:2751:13:HAL_ADC_LevelOutOfWindowCallback	0	static
stm32h7xx_hal_adc.c:2773:13:HAL_ADC_ErrorCallback	0	static
stm32h7xx_hal_adc.c:2361:6:HAL_ADC_IRQHandler	24	static
stm32h7xx_hal_adc.c:3816:6:ADC_DMAConvCplt	8	static
stm32h7xx_hal_adc.c:3912:6:ADC_DMAError	8	static
stm32h7xx_hal_adc.c:2819:19:HAL_ADC_ConfigChannel	32	static
stm32h7xx_hal_adc.c:3135:19:HAL_ADC_AnalogWDGConfig	16	static
stm32h7xx_hal_adc.c:3497:10:HAL_ADC_GetState	0	static
stm32h7xx_hal_adc.c:3511:10:HAL_ADC_GetError	0	static
stm32h7xx_hal_adc.c:3541:19:ADC_ConversionStop	16	static
stm32h7xx_hal_adc.c:3675:19:ADC_Enable	16	static
stm32h7xx_hal_adc.c:1397:19:HAL_ADC_Start	16	static
stm32h7xx_hal_adc.c:1884:19:HAL_ADC_Start_IT	16	static
stm32h7xx_hal_adc.c:2112:19:HAL_ADC_Start_DMA	24	static
stm32h7xx_hal_adc.c:3754:19:ADC_Disable	16	static
stm32h7xx_hal_adc.c:842:19:HAL_ADC_DeInit	16	static
stm32h7xx_hal_adc.c:1518:19:HAL_ADC_Stop	8	static
stm32h7xx_hal_adc.c:2059:19:HAL_ADC_Stop_IT	8	static
stm32h7xx_hal_adc.c:2259:19:HAL_ADC_Stop_DMA	16	static
stm32h7xx_hal_adc.c:3938:6:ADC_ConfigureBoostMode	16	static
stm32h7xx_hal_adc.c:407:19:HAL_ADC_Init	24	static
