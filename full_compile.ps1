# STM32H745I TouchGFX Full Project Compilation Script
Write-Host "Starting full STM32H745I TouchGFX project compilation..."

# Set environment variables
$env:PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin;C:\ST\STM32CubeCLT_1.18.0\CMake\bin;C:\ST\STM32CubeCLT_1.18.0\Ninja\bin;" + $env:PATH

# Define tools
$GCC = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe"
$GPP = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-g++.exe"

# Common compiler flags
$COMMON_FLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb", 
    "-mfpu=fpv5-sp-d16",
    "-mfloat-abi=hard",
    "-DUSE_HAL_DRIVER",
    "-DSTM32H745xx", 
    "-DCORE_CM7",
    "-DUSE_PWR_DIRECT_SMPS_SUPPLY",
    "-DST",
    "-DUSE_STM32H745I_DISCO",
    "-DUSE_PWR_LDO_SUPPLY__N",
    "-DUSE_FLOATING_POINT",
    "-DUSE_BPP=16"
)

# Include paths
$TOUCHGFX_PATH = "C:\Users\<USER>\STMicroelectronics\TouchGFX\4.25.0\touchgfx\framework\include"
$INCLUDE_PATHS = @(
    "-ICM7/Core/Inc",
    "-ICM7/TouchGFX/App",
    "-ICM7/TouchGFX/target/generated", 
    "-ICM7/TouchGFX/target",
    "-ICM7/TouchGFX/gui/include",
    "-ICM7/TouchGFX/generated/gui_generated/include",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy",
    "-IDrivers/CMSIS/Device/ST/STM32H7xx/Include",
    "-IDrivers/CMSIS/Include",
    "-IDrivers/BSP/STM32H745I-DISCO",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/include",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F",
    "-I$TOUCHGFX_PATH"
)

# C compiler flags
$CFLAGS = $COMMON_FLAGS + @(
    "--specs=nano.specs",
    "-Os",
    "-Wall",
    "-fdata-sections", 
    "-ffunction-sections",
    "-fstack-usage",
    "-g3",
    "-std=gnu11",
    "-MMD",
    "-MP"
)

# C++ compiler flags  
$CPPFLAGS = $COMMON_FLAGS + @(
    "--specs=nano.specs",
    "-Os", 
    "-fdata-sections",
    "-fno-exceptions",
    "-fno-rtti",
    "-fno-use-cxa-atexit",
    "-Wall",
    "-femit-class-debug-always",
    "-fstack-usage",
    "-g3",
    "-std=c++17",
    "-MMD",
    "-MP"
)

# Create output directory
$OUTPUT_DIR = "build_full"
if (Test-Path $OUTPUT_DIR) {
    Remove-Item -Recurse -Force $OUTPUT_DIR
}
New-Item -ItemType Directory -Path $OUTPUT_DIR

Write-Host "Starting full project compilation..."

# Function to compile C files
function Compile-CFile {
    param($SourceFile, $OutputFile)
    Write-Host "Compiling C: $SourceFile"
    $cmd_args = $CFLAGS + $INCLUDE_PATHS + @("-c", $SourceFile, "-o", "$OUTPUT_DIR/$OutputFile")
    & $GCC @cmd_args
    return $LASTEXITCODE
}

# Function to compile C++ files
function Compile-CppFile {
    param($SourceFile, $OutputFile)
    Write-Host "Compiling C++: $SourceFile"
    $cmd_args = $CPPFLAGS + $INCLUDE_PATHS + @("-c", $SourceFile, "-o", "$OUTPUT_DIR/$OutputFile")
    & $GPP @cmd_args
    return $LASTEXITCODE
}

$ErrorCount = 0

# Compile main C files
$CFiles = @(
    @("CM7/Core/Src/main.c", "main.o"),
    @("CM7/Core/Src/stm32h7xx_it.c", "stm32h7xx_it.o"),
    @("CM7/Core/Src/stm32h7xx_hal_msp.c", "stm32h7xx_hal_msp.o"),
    @("CM7/Core/Src/system_stm32h7xx.c", "system_stm32h7xx.o")
)

foreach ($file in $CFiles) {
    if (Test-Path $file[0]) {
        $result = Compile-CFile $file[0] $file[1]
        if ($result -ne 0) {
            Write-Host "❌ Failed to compile $($file[0])" -ForegroundColor Red
            $ErrorCount++
        } else {
            Write-Host "✅ Successfully compiled $($file[0])" -ForegroundColor Green
        }
    } else {
        Write-Host "⚠️ File not found: $($file[0])" -ForegroundColor Yellow
    }
}

# Compile TouchGFX C++ files
$CppFiles = @(
    @("CM7/TouchGFX/gui/src/model/Model.cpp", "Model.o"),
    @("CM7/TouchGFX/gui/src/screen1_screen/Screen1Presenter.cpp", "Screen1Presenter.o"),
    @("CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp", "Screen1View.o")
)

foreach ($file in $CppFiles) {
    if (Test-Path $file[0]) {
        $result = Compile-CppFile $file[0] $file[1]
        if ($result -ne 0) {
            Write-Host "❌ Failed to compile $($file[0])" -ForegroundColor Red
            $ErrorCount++
        } else {
            Write-Host "✅ Successfully compiled $($file[0])" -ForegroundColor Green
        }
    } else {
        Write-Host "⚠️ File not found: $($file[0])" -ForegroundColor Yellow
    }
}

Write-Host "`n=== Compilation Summary ===" -ForegroundColor Cyan
if ($ErrorCount -eq 0) {
    Write-Host "🎉 All files compiled successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ $ErrorCount files failed to compile" -ForegroundColor Red
}

# List generated object files
if (Test-Path $OUTPUT_DIR) {
    Write-Host "`nGenerated object files:" -ForegroundColor Cyan
    Get-ChildItem $OUTPUT_DIR -Filter "*.o" | ForEach-Object {
        $size = [math]::Round($_.Length / 1KB, 2)
        Write-Host "  $($_.Name) ($size KB)"
    }
}
