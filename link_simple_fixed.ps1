# 简化的TouchGFX链接脚本
Write-Host "开始TouchGFX项目链接..." -ForegroundColor Green

# 工具链路径检测
$POSSIBLE_PATHS = @(
    "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin",
    "C:\Program Files\STMicroelectronics\STM32Cube\STM32CubeProgrammer\bin",
    "C:\ST\STM32CubeCLT_1.16.0\GNU-tools-for-STM32\bin"
)

$TOOLCHAIN_PATH = $null
foreach ($path in $POSSIBLE_PATHS) {
    if (Test-Path "$path\arm-none-eabi-gcc.exe") {
        $TOOLCHAIN_PATH = $path
        break
    }
}

if (-not $TOOLCHAIN_PATH) {
    Write-Host "错误: 找不到STM32工具链!" -ForegroundColor Red
    Write-Host "尝试使用默认路径..." -ForegroundColor Yellow
    $TOOLCHAIN_PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin"
}

Write-Host "使用工具链: $TOOLCHAIN_PATH" -ForegroundColor Cyan

$GCC = "$TOOLCHAIN_PATH\arm-none-eabi-gcc.exe"
$OBJCOPY = "$TOOLCHAIN_PATH\arm-none-eabi-objcopy.exe"

# 编译存根文件
Write-Host "编译存根文件..." -ForegroundColor Yellow

$compile_cmd = @(
    "cmd", "/c", "`"$GCC`"",
    "-mcpu=cortex-m7",
    "-mthumb", 
    "-mfpu=fpv5-sp-d16",
    "-mfloat-abi=hard",
    "-O2",
    "-c",
    "touchgfx_stubs.cpp",
    "-o",
    "build_touchgfx_complete/touchgfx_stubs.o"
)

& $compile_cmd[0] $compile_cmd[1..($compile_cmd.Length-1)]

if ($LASTEXITCODE -ne 0) {
    Write-Host "存根文件编译失败!" -ForegroundColor Red
    exit 1
}

Write-Host "存根文件编译成功!" -ForegroundColor Green

# 链接命令
Write-Host "开始链接..." -ForegroundColor Yellow

$link_cmd = @(
    "cmd", "/c", "`"$GCC`"",
    "-mcpu=cortex-m7",
    "-mthumb",
    "-mfpu=fpv5-sp-d16", 
    "-mfloat-abi=hard",
    "--specs=nano.specs",
    "--specs=nosys.specs",
    "-Wl,--gc-sections",
    "-static",
    "-Wl,--start-group",
    "-lc",
    "-lm",
    "-lstdc++",
    "-lsupc++",
    "-lgcc",
    "-Wl,--end-group",
    "-Tgcc/STM32H745XIHX_FLASH_CM7.ld",
    "@link_objects.txt",
    "build_touchgfx_complete/touchgfx_stubs.o",
    "CM7/Middlewares/ST/touchgfx/lib/core/cortex_m7/gcc/libtouchgfx-float-abi-hard.a",
    "-o",
    "build_touchgfx_complete/TouchGFX_Complete_Fixed.elf"
)

& $link_cmd[0] $link_cmd[1..($link_cmd.Length-1)]

if ($LASTEXITCODE -eq 0) {
    Write-Host "链接成功!" -ForegroundColor Green
    
    # 生成HEX文件
    Write-Host "生成HEX文件..." -ForegroundColor Yellow
    $hex_cmd = @("cmd", "/c", "`"$OBJCOPY`"", "-O", "ihex", "build_touchgfx_complete/TouchGFX_Complete_Fixed.elf", "build_touchgfx_complete/TouchGFX_Complete_Fixed.hex")
    & $hex_cmd[0] $hex_cmd[1..($hex_cmd.Length-1)]
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "编译完成!" -ForegroundColor Green
        Write-Host "输出文件:" -ForegroundColor White
        Write-Host "  ELF: build_touchgfx_complete/TouchGFX_Complete_Fixed.elf" -ForegroundColor Cyan
        Write-Host "  HEX: build_touchgfx_complete/TouchGFX_Complete_Fixed.hex" -ForegroundColor Cyan
        
        # 显示文件大小
        if (Test-Path "build_touchgfx_complete/TouchGFX_Complete_Fixed.elf") {
            $fileSize = (Get-Item "build_touchgfx_complete/TouchGFX_Complete_Fixed.elf").Length
            Write-Host "ELF文件大小: $([math]::Round($fileSize/1024, 2)) KB" -ForegroundColor Green
        }
        
        Write-Host ""
        Write-Host "项目编译成功! 可以使用STM32CubeProgrammer下载到开发板" -ForegroundColor Green
        Write-Host "TouchGFX界面将显示ADC1控制的仪表盘" -ForegroundColor Cyan
    } else {
        Write-Host "HEX文件生成失败!" -ForegroundColor Red
    }
} else {
    Write-Host "链接失败!" -ForegroundColor Red
    Write-Host "请检查错误信息" -ForegroundColor Yellow
}
