<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>STM32H745I_DISCO_CM7</name>
	<comment/>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.st.stm32cube.ide.mcu.MCUProjectNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUCubeIdeServicesRevAev2ProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUNonUnderRootProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUCubeProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUAdvancedStructureProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUMultiCpuProjectNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>Utilities/jpeg_utils.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Utilities/JPEG/jpeg_utils.c</locationURI>
		</link>
		<link>
			<name>Drivers/CMSIS/system_stm32h7xx_dualcore_boot_cm4_cm7.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_adc.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_adc_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_cortex.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_crc.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_crc_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_dma.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_dma2d.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_dma_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_exti.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_flash.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_flash_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_gpio.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_hsem.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_i2c.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_i2c_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_jpeg.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_jpeg.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_ltdc.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_ltdc_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_mdma.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_pwr.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_pwr_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_qspi.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_rcc.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_rcc_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_sdram.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_tim.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_tim_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_ll_fmc.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FreeRTOS/cmsis_os2.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FreeRTOS/croutine.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Middlewares/Third_Party/FreeRTOS/Source/croutine.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FreeRTOS/event_groups.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Middlewares/Third_Party/FreeRTOS/Source/event_groups.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FreeRTOS/heap_4.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FreeRTOS/list.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Middlewares/Third_Party/FreeRTOS/Source/list.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FreeRTOS/port.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FreeRTOS/queue.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Middlewares/Third_Party/FreeRTOS/Source/queue.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FreeRTOS/stream_buffer.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FreeRTOS/tasks.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Middlewares/Third_Party/FreeRTOS/Source/tasks.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FreeRTOS/timers.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Middlewares/Third_Party/FreeRTOS/Source/timers.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/freertos.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/Core/Src/freertos.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/main.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/Core/Src/main.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/main_user.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/Core/Src/main_user.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/stm32h7xx_hal_msp.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/Core/Src/stm32h7xx_hal_msp.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/stm32h7xx_hal_timebase_tim.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/Core/Src/stm32h7xx_hal_timebase_tim.c</locationURI>
		</link>
		<link>
			<name>Application/User/Core/stm32h7xx_it.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/Core/Src/stm32h7xx_it.c</locationURI>
		</link>
		<link>
			<name>Drivers/BSP/Components/ft5336.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/BSP/Components/ft5336/ft5336.c</locationURI>
		</link>
		<link>
			<name>Drivers/BSP/Components/ft5336_reg.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/BSP/Components/ft5336/ft5336_reg.c</locationURI>
		</link>
		<link>
			<name>Drivers/BSP/Components/mt25tl01g.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/BSP/Components/mt25tl01g/mt25tl01g.c</locationURI>
		</link>
		<link>
			<name>Drivers/BSP/Components/mt48lc4m32b2.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/BSP/Components/mt48lc4m32b2/mt48lc4m32b2.c</locationURI>
		</link>
		<link>
			<name>Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery.c</locationURI>
		</link>
		<link>
			<name>Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_bus.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_bus.c</locationURI>
		</link>
		<link>
			<name>Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_lcd.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_lcd.c</locationURI>
		</link>
		<link>
			<name>Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_qspi.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_qspi.c</locationURI>
		</link>
		<link>
			<name>Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_sdram.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_sdram.c</locationURI>
		</link>
		<link>
			<name>Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_ts.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_ts.c</locationURI>
		</link>
		<link>
			<name>Application/User/TouchGFX/App/app_touchgfx.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/App/app_touchgfx.c</locationURI>
		</link>
		<link>
			<name>Application/User/TouchGFX/target/STM32H7Instrumentation.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/target/STM32H7Instrumentation.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/TouchGFX/target/STM32TouchController.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/target/STM32TouchController.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/TouchGFX/target/TouchGFXGPIO.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/target/TouchGFXGPIO.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/TouchGFX/target/TouchGFXHAL.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/target/TouchGFXHAL.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/TouchGFX/target/stm32h745i_touchcontroller.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/target/stm32h745i_touchcontroller.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/TouchGFX/target/generated/HardwareMJPEGDecoder.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/target/generated/HardwareMJPEGDecoder.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/TouchGFX/target/generated/OSWrappers.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/target/generated/OSWrappers.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/TouchGFX/target/generated/STM32DMA.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/target/generated/STM32DMA.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/TouchGFX/target/generated/TouchGFXConfiguration.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/target/generated/TouchGFXConfiguration.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/TouchGFX/target/generated/TouchGFXGeneratedHAL.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/target/generated/TouchGFXGeneratedHAL.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/gui/FrontendApplication.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/gui/src/common/FrontendApplication.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/gui/Model.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/gui/src/model/Model.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/gui/Screen1Presenter.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/gui/src/screen1_screen/Screen1Presenter.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/gui/Screen1View.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/ApplicationFontProvider.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/ApplicationFontProvider.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/CachedFont.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/CachedFont.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/CompressedFontCache.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/CompressedFontCache.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/CompressedUnmappedFontCache.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/CompressedUnmappedFontCache.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/FontCache.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/FontCache.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/Font_verdana_10_4bpp_0.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/Font_verdana_10_4bpp_0.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/Font_verdana_20_4bpp_0.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/Font_verdana_20_4bpp_0.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/Font_verdana_40_4bpp_0.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/Font_verdana_40_4bpp_0.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/GeneratedFont.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/GeneratedFont.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/Kerning_verdana_10_4bpp.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/Kerning_verdana_10_4bpp.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/Kerning_verdana_20_4bpp.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/Kerning_verdana_20_4bpp.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/Kerning_verdana_40_4bpp.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/Kerning_verdana_40_4bpp.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/Table_verdana_10_4bpp.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/Table_verdana_10_4bpp.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/Table_verdana_20_4bpp.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/Table_verdana_20_4bpp.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/Table_verdana_40_4bpp.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/Table_verdana_40_4bpp.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/UnmappedDataFont.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/UnmappedDataFont.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/VectorFontRendererBuffers.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/fonts/src/VectorFontRendererBuffers.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/FrontendApplicationBase.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/gui_generated/src/common/FrontendApplicationBase.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/Screen1ViewBase.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/gui_generated/src/screen1_screen/Screen1ViewBase.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/BitmapDatabase.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/BitmapDatabase.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/SVGDatabase.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/SVGDatabase.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_alternate_theme_images_widgets_gauge_small_backgrounds_active.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/__generated/image_alternate_theme_images_widgets_gauge_small_backgrounds_active.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_alternate_theme_images_widgets_gauge_small_backgrounds_light.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/__generated/image_alternate_theme_images_widgets_gauge_small_backgrounds_light.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_alternate_theme_images_widgets_gauge_small_fillers_swoop.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/__generated/image_alternate_theme_images_widgets_gauge_small_fillers_swoop.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_alternate_theme_images_widgets_gauge_small_needles_rough.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/__generated/image_alternate_theme_images_widgets_gauge_small_needles_rough.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_VC1.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/image_VC1.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_VC2.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/image_VC2.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_VC3.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/image_VC3.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_VC4.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/image_VC4.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_VC5.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/image_VC5.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_VC6.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/image_VC6.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_VC7.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/image_VC7.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/image_VC8.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/images/src/image_VC8.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/LanguageGb.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/texts/src/LanguageGb.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/Texts.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/texts/src/Texts.cpp</locationURI>
		</link>
		<link>
			<name>Application/User/generated/TypedTextDatabase.cpp</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/CM7/TouchGFX/generated/texts/src/TypedTextDatabase.cpp</locationURI>
		</link>
	</linkedResources>
</projectDescription>
