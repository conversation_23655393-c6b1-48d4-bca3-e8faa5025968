@echo off
echo Starting TouchGFX project linking with stubs...

REM Set toolchain path
set TOOLCHAIN_PATH=C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin
set GCC=%TOOLCHAIN_PATH%\arm-none-eabi-gcc.exe
set OBJCOPY=%TOOLCHAIN_PATH%\arm-none-eabi-objcopy.exe

echo Using toolchain: %TOOLCHAIN_PATH%

REM Compile stub file
echo Compiling stub file...
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -O2 -c touchgfx_stubs.cpp -o build_touchgfx_complete/touchgfx_stubs.o

if %ERRORLEVEL% neq 0 (
    echo Stub compilation failed!
    pause
    exit /b 1
)

echo Stub compilation successful!

REM Link command
echo Starting linking...
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard --specs=nano.specs --specs=nosys.specs -Wl,--gc-sections -static -Wl,--start-group -lc -lm -lstdc++ -lsupc++ -lgcc -Wl,--end-group -Tgcc/STM32H745XIHX_FLASH_CM7.ld @link_objects.txt build_touchgfx_complete/touchgfx_stubs.o CM7/Middlewares/ST/touchgfx/lib/core/cortex_m7/gcc/libtouchgfx-float-abi-hard.a -o build_touchgfx_complete/TouchGFX_Complete_Fixed.elf

if %ERRORLEVEL% equ 0 (
    echo Linking successful!
    
    REM Generate HEX file
    echo Generating HEX file...
    "%OBJCOPY%" -O ihex build_touchgfx_complete/TouchGFX_Complete_Fixed.elf build_touchgfx_complete/TouchGFX_Complete_Fixed.hex
    
    if %ERRORLEVEL% equ 0 (
        echo Compilation complete!
        echo Output files:
        echo   ELF: build_touchgfx_complete/TouchGFX_Complete_Fixed.elf
        echo   HEX: build_touchgfx_complete/TouchGFX_Complete_Fixed.hex
        echo.
        echo Project compiled successfully! Ready for STM32CubeProgrammer download
        echo TouchGFX interface will display ADC1 controlled gauge
    ) else (
        echo HEX file generation failed!
    )
) else (
    echo Linking failed!
    echo Please check error messages
)

pause
