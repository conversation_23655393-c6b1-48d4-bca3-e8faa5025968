# 简单链接TouchGFX固件
Write-Host "=== 链接TouchGFX完整固件 ===" -ForegroundColor Green

$GCC = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe"
$OBJCOPY = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-objcopy.exe"
$SIZE = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-size.exe"

# 收集所有目标文件
$ObjectFiles = Get-ChildItem "build_touchgfx_complete\*.o" | ForEach-Object { $_.FullName }
Write-Host "找到 $($ObjectFiles.Count) 个目标文件" -ForegroundColor Yellow

# 链接标志
$LinkFlags = @(
    "-mcpu=cortex-m7",
    "-mthumb", 
    "-mfpu=fpv5-sp-d16",
    "-mfloat-abi=hard",
    "--specs=nano.specs",
    "--specs=nosys.specs",
    "-Wl,--gc-sections",
    "-static",
    "-Wl,--start-group",
    "-lc",
    "-lm", 
    "-Wl,--end-group",
    "-Tgcc\STM32H745XIHX_FLASH_CM7.ld"
)

Write-Host "开始链接..." -ForegroundColor Yellow

# 执行链接
$LinkCommand = @($GCC) + $LinkFlags + $ObjectFiles + @("-o", "build_touchgfx_complete\TouchGFX_Complete.elf")

# 执行链接
& $LinkCommand[0] $LinkCommand[1..($LinkCommand.Length-1)]

if ($LASTEXITCODE -eq 0) {
    Write-Host "链接成功!" -ForegroundColor Green

    # 生成HEX和BIN文件
    Write-Host "生成HEX文件..." -ForegroundColor Yellow
    & $OBJCOPY "-O" "ihex" "build_touchgfx_complete\TouchGFX_Complete.elf" "build_touchgfx_complete\TouchGFX_Complete.hex"

    Write-Host "生成BIN文件..." -ForegroundColor Yellow
    & $OBJCOPY "-O" "binary" "build_touchgfx_complete\TouchGFX_Complete.elf" "build_touchgfx_complete\TouchGFX_Complete.bin"

    # 显示大小
    Write-Host "程序大小信息:" -ForegroundColor Green
    & $SIZE "build_touchgfx_complete\TouchGFX_Complete.elf"

    Write-Host "`n固件文件已生成:" -ForegroundColor Green
    Write-Host "  ELF: build_touchgfx_complete\TouchGFX_Complete.elf" -ForegroundColor White
    Write-Host "  HEX: build_touchgfx_complete\TouchGFX_Complete.hex" -ForegroundColor White
    Write-Host "  BIN: build_touchgfx_complete\TouchGFX_Complete.bin" -ForegroundColor White

    # 询问是否下载
    $download = Read-Host "`n是否下载到STM32H745I-DISCO开发板? (y/n)"
    if ($download -eq "y" -or $download -eq "Y") {
        Write-Host "下载固件到开发板..." -ForegroundColor Yellow

        $PROGRAMMER = "C:\ST\STM32CubeCLT_1.18.0\STM32CubeProgrammer\bin\STM32_Programmer_CLI.exe"
        & $PROGRAMMER "-c" "port=SWD" "freq=4000" "-w" "build_touchgfx_complete\TouchGFX_Complete.hex" "-v" "-rst"

        if ($LASTEXITCODE -eq 0) {
            Write-Host "固件下载成功!" -ForegroundColor Green
            Write-Host "TouchGFX界面应该显示，ADC1(PA1)控制右侧仪表盘" -ForegroundColor Cyan
        } else {
            Write-Host "固件下载失败!" -ForegroundColor Red
        }
    }
} else {
    Write-Host "链接失败!" -ForegroundColor Red
    exit 1
}

Write-Host "完成!" -ForegroundColor Green
