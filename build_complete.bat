@echo off
echo === STM32H745I TouchGFX Complete Build ===

REM 设置工具链路径
set TOOLCHAIN=C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin
set GCC=%TOOLCHAIN%\arm-none-eabi-gcc.exe
set GPP=%TOOLCHAIN%\arm-none-eabi-g++.exe
set OBJCOPY=%TOOLCHAIN%\arm-none-eabi-objcopy.exe
set SIZE=%TOOLCHAIN%\arm-none-eabi-size.exe

REM 检查工具链
if not exist "%GCC%" (
    echo 错误: 找不到GCC编译器: %GCC%
    exit /b 1
)

REM 编译标志
set CFLAGS=-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 --specs=nano.specs -Os -Wall -fdata-sections -ffunction-sections -g3 -std=gnu11
set CPPFLAGS=-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 --specs=nano.specs -Os -Wall -fdata-sections -ffunction-sections -fno-exceptions -fno-rtti -g3 -std=c++17

REM 包含路径
set INCLUDES=-ICM7/Core/Inc -ICM7/TouchGFX/App -ICM7/TouchGFX/target/generated -ICM7/TouchGFX/target -ICM7/TouchGFX/gui/include -ICM7/TouchGFX/generated/gui_generated/include -IDrivers/STM32H7xx_HAL_Driver/Inc -IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy -IDrivers/CMSIS/Device/ST/STM32H7xx/Include -IDrivers/CMSIS/Include -IDrivers/BSP/STM32H745I-DISCO -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -IMiddlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:\Users\<USER>\STMicroelectronics\TouchGFX\4.25.0\touchgfx\framework\include

REM 创建构建目录
set BUILD_DIR=build_batch
if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%
mkdir %BUILD_DIR%

echo 编译启动文件...
"%GCC%" %CFLAGS% %INCLUDES% -c gcc/startup_stm32h745xihx_cm7.s -o %BUILD_DIR%/startup.o
if %ERRORLEVEL% neq 0 (
    echo 启动文件编译失败!
    exit /b 1
)

echo 编译核心文件...
"%GCC%" %CFLAGS% %INCLUDES% -c CM7/Core/Src/main.c -o %BUILD_DIR%/main.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c CM7/Core/Src/stm32h7xx_it.c -o %BUILD_DIR%/stm32h7xx_it.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c CM7/Core/Src/stm32h7xx_hal_msp.c -o %BUILD_DIR%/stm32h7xx_hal_msp.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c CM7/Core/Src/stm32h7xx_hal_timebase_tim.c -o %BUILD_DIR%/stm32h7xx_hal_timebase_tim.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c CM7/Core/Src/freertos.c -o %BUILD_DIR%/freertos.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c CM7/Core/Src/main_user.c -o %BUILD_DIR%/main_user.o
if %ERRORLEVEL% neq 0 goto :error

echo 编译HAL驱动...
"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c -o %BUILD_DIR%/stm32h7xx_hal.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c -o %BUILD_DIR%/stm32h7xx_hal_adc.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c -o %BUILD_DIR%/stm32h7xx_hal_adc_ex.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c -o %BUILD_DIR%/stm32h7xx_hal_cortex.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c -o %BUILD_DIR%/stm32h7xx_hal_dma.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c -o %BUILD_DIR%/stm32h7xx_hal_dma_ex.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.c -o %BUILD_DIR%/stm32h7xx_hal_dma2d.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c -o %BUILD_DIR%/stm32h7xx_hal_gpio.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c -o %BUILD_DIR%/stm32h7xx_hal_rcc.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c -o %BUILD_DIR%/stm32h7xx_hal_rcc_ex.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c -o %BUILD_DIR%/stm32h7xx_hal_pwr.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c -o %BUILD_DIR%/stm32h7xx_hal_pwr_ex.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c -o %BUILD_DIR%/stm32h7xx_hal_tim.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c -o %BUILD_DIR%/stm32h7xx_hal_tim_ex.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.c -o %BUILD_DIR%/stm32h7xx_hal_ltdc.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.c -o %BUILD_DIR%/stm32h7xx_hal_ltdc_ex.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.c -o %BUILD_DIR%/stm32h7xx_hal_sdram.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.c -o %BUILD_DIR%/stm32h7xx_ll_fmc.o
if %ERRORLEVEL% neq 0 goto :error

echo 编译TouchGFX应用...
"%GCC%" %CFLAGS% %INCLUDES% -c CM7/TouchGFX/App/app_touchgfx.c -o %BUILD_DIR%/app_touchgfx.o
if %ERRORLEVEL% neq 0 goto :error

echo 编译FreeRTOS...
"%GCC%" %CFLAGS% %INCLUDES% -c Middlewares/Third_Party/FreeRTOS/Source/tasks.c -o %BUILD_DIR%/tasks.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Middlewares/Third_Party/FreeRTOS/Source/queue.c -o %BUILD_DIR%/queue.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Middlewares/Third_Party/FreeRTOS/Source/list.c -o %BUILD_DIR%/list.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Middlewares/Third_Party/FreeRTOS/Source/timers.c -o %BUILD_DIR%/timers.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.c -o %BUILD_DIR%/port.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c -o %BUILD_DIR%/heap_4.o
if %ERRORLEVEL% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c -o %BUILD_DIR%/cmsis_os2.o
if %ERRORLEVEL% neq 0 goto :error

echo 编译TouchGFX C++文件...
"%GPP%" %CPPFLAGS% %INCLUDES% -c CM7/TouchGFX/gui/src/model/Model.cpp -o %BUILD_DIR%/Model.o
if %ERRORLEVEL% neq 0 goto :error

"%GPP%" %CPPFLAGS% %INCLUDES% -c CM7/TouchGFX/gui/src/screen1_screen/Screen1Presenter.cpp -o %BUILD_DIR%/Screen1Presenter.o
if %ERRORLEVEL% neq 0 goto :error

"%GPP%" %CPPFLAGS% %INCLUDES% -c CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp -o %BUILD_DIR%/Screen1View.o
if %ERRORLEVEL% neq 0 goto :error

"%GPP%" %CPPFLAGS% %INCLUDES% -c CM7/TouchGFX/target/TouchGFXHAL.cpp -o %BUILD_DIR%/TouchGFXHAL.o
if %ERRORLEVEL% neq 0 goto :error

"%GPP%" %CPPFLAGS% %INCLUDES% -c CM7/TouchGFX/target/TouchGFXGPIO.cpp -o %BUILD_DIR%/TouchGFXGPIO.o
if %ERRORLEVEL% neq 0 goto :error

"%GPP%" %CPPFLAGS% %INCLUDES% -c CM7/TouchGFX/target/STM32TouchController.cpp -o %BUILD_DIR%/STM32TouchController.o
if %ERRORLEVEL% neq 0 goto :error

"%GPP%" %CPPFLAGS% %INCLUDES% -c CM7/TouchGFX/target/STM32H7Instrumentation.cpp -o %BUILD_DIR%/STM32H7Instrumentation.o
if %ERRORLEVEL% neq 0 goto :error

echo 链接...
set LDFLAGS=-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard --specs=nano.specs --specs=nosys.specs -Wl,--gc-sections -static --specs=nano.specs -Wl,--start-group -lc -lm -Wl,--end-group

"%GCC%" %LDFLAGS% -Tgcc/STM32H745XIHX_FLASH_CM7.ld %BUILD_DIR%/*.o -o %BUILD_DIR%/MyApplication_4.elf

if %ERRORLEVEL% eq 0 (
    echo 链接成功!
    
    REM 生成HEX和BIN文件
    "%OBJCOPY%" -O ihex %BUILD_DIR%/MyApplication_4.elf %BUILD_DIR%/MyApplication_4.hex
    "%OBJCOPY%" -O binary %BUILD_DIR%/MyApplication_4.elf %BUILD_DIR%/MyApplication_4.bin
    
    REM 显示大小
    "%SIZE%" %BUILD_DIR%/MyApplication_4.elf
    
    echo 编译完成! 输出文件:
    echo   ELF: %BUILD_DIR%/MyApplication_4.elf
    echo   HEX: %BUILD_DIR%/MyApplication_4.hex
    echo   BIN: %BUILD_DIR%/MyApplication_4.bin
    
    goto :success
) else (
    echo 链接失败!
    exit /b 1
)

:error
echo 编译失败!
exit /b 1

:success
echo 编译成功完成!
