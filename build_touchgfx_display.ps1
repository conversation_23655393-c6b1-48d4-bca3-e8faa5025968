# TouchGFX Display版本编译脚本 - 解决白屏问题
# 包含LCD初始化和基本显示功能

Write-Host "开始编译TouchGFX Display版本..." -ForegroundColor Green

# 设置工具链路径
$GCC_PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin"
$PROGRAMMER_PATH = "C:\ST\STM32CubeCLT_1.18.0\STM32CubeProgrammer\bin"

# 检查工具链
if (-not (Test-Path "$GCC_PATH\arm-none-eabi-gcc.exe")) {
    Write-Host "错误: 找不到GCC工具链" -ForegroundColor Red
    exit 1
}

# 编译器设置
$CC = "$GCC_PATH\arm-none-eabi-gcc.exe"
$OBJCOPY = "$GCC_PATH\arm-none-eabi-objcopy.exe"
$SIZE = "$GCC_PATH\arm-none-eabi-size.exe"

# 编译选项
$CFLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb",
    "-mfpu=fpv5-sp-d16", 
    "-mfloat-abi=hard",
    "-DUSE_HAL_DRIVER",
    "-DSTM32H745xx",
    "-DCORE_CM7",
    "-DUSE_PWR_DIRECT_SMPS_SUPPLY",
    "-DDATA_IN_D2_SRAM",
    "-O2",
    "-Wall",
    "-fdata-sections",
    "-ffunction-sections",
    "-g",
    "-gdwarf-2"
)

# 包含路径
$INCLUDES = @(
    "-IDrivers/STM32H7xx_HAL_Driver/Inc",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy", 
    "-IDrivers/CMSIS/Device/ST/STM32H7xx/Include",
    "-IDrivers/CMSIS/Include",
    "-IDrivers/BSP/STM32H745I-DISCO",
    "-IDrivers/BSP/Components/Common",
    "-IDrivers/BSP/Components/otm8009a",
    "-IDrivers/BSP/Components/ft5336",
    "-IDrivers/BSP/Components/is42s32800j",
    "-IDrivers/BSP/Components/mt25tl01g",
    "-ICM7/Core/Inc",
    "-ICM7/TouchGFX/target/generated",
    "-ICM7/TouchGFX/target"
)

# 源文件列表
$SOURCES = @(
    "touchgfx_with_display.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dsi.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.c",
    "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery.c",
    "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_lcd.c",
    "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_ts.c",
    "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_sdram.c",
    "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_qspi.c",
    "Drivers/BSP/Components/otm8009a/otm8009a.c",
    "Drivers/BSP/Components/ft5336/ft5336.c",
    "Drivers/BSP/Components/is42s32800j/is42s32800j.c",
    "Drivers/BSP/Components/mt25tl01g/mt25tl01g.c",
    "gcc/startup_stm32h745xihx_cm7.s"
)

# 链接选项
$LDFLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb", 
    "-mfpu=fpv5-sp-d16",
    "-mfloat-abi=hard",
    "-specs=nano.specs",
    "-T", "gcc/STM32H745XIHX_FLASH_CM7.ld",
    "-Wl,--gc-sections",
    "-Wl,--print-memory-usage"
)

Write-Host "编译源文件..." -ForegroundColor Yellow

# 创建输出目录
if (-not (Test-Path "build")) {
    New-Item -ItemType Directory -Path "build" | Out-Null
}

# 编译所有源文件
$OBJECTS = @()
foreach ($source in $SOURCES) {
    if (Test-Path $source) {
        $obj_name = [System.IO.Path]::GetFileNameWithoutExtension($source) + ".o"
        $obj_path = "build/$obj_name"
        $OBJECTS += $obj_path
        
        Write-Host "编译: $source" -ForegroundColor Cyan
        
        if ($source.EndsWith(".s")) {
            # 汇编文件
            $cmd = @($CC) + $CFLAGS + $INCLUDES + @("-c", $source, "-o", $obj_path)
        } else {
            # C文件
            $cmd = @($CC) + $CFLAGS + $INCLUDES + @("-c", $source, "-o", $obj_path)
        }
        
        $result = & $cmd[0] $cmd[1..($cmd.Length-1)] 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "编译失败: $source" -ForegroundColor Red
            Write-Host $result -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "警告: 源文件不存在: $source" -ForegroundColor Yellow
    }
}

Write-Host "链接..." -ForegroundColor Yellow

# 链接生成ELF文件
$ELF_FILE = "build/touchgfx_display.elf"
$cmd = @($CC) + $LDFLAGS + $OBJECTS + @("-o", $ELF_FILE)

$result = & $cmd[0] $cmd[1..($cmd.Length-1)] 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "链接失败" -ForegroundColor Red
    Write-Host $result -ForegroundColor Red
    exit 1
}

Write-Host "生成HEX文件..." -ForegroundColor Yellow

# 生成HEX文件
$HEX_FILE = "build/touchgfx_display.hex"
& $OBJCOPY -O ihex $ELF_FILE $HEX_FILE

if ($LASTEXITCODE -ne 0) {
    Write-Host "生成HEX文件失败" -ForegroundColor Red
    exit 1
}

# 显示大小信息
Write-Host "固件大小信息:" -ForegroundColor Green
& $SIZE $ELF_FILE

Write-Host "编译成功! 输出文件:" -ForegroundColor Green
Write-Host "  ELF: $ELF_FILE" -ForegroundColor White
Write-Host "  HEX: $HEX_FILE" -ForegroundColor White

# 询问是否下载到开发板
$download = Read-Host "是否立即下载到开发板? (y/n)"
if ($download -eq "y" -or $download -eq "Y") {
    Write-Host "开始下载固件..." -ForegroundColor Green
    
    if (Test-Path "$PROGRAMMER_PATH\STM32_Programmer_CLI.exe") {
        $PROGRAMMER = "$PROGRAMMER_PATH\STM32_Programmer_CLI.exe"
        
        # 下载固件
        & $PROGRAMMER -c port=SWD -w $HEX_FILE 0x08000000 -v -rst
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "固件下载成功!" -ForegroundColor Green
            Write-Host "开发板应该显示蓝色背景和测试文本，不再是白屏" -ForegroundColor Cyan
        } else {
            Write-Host "固件下载失败" -ForegroundColor Red
        }
    } else {
        Write-Host "找不到STM32_Programmer_CLI" -ForegroundColor Red
    }
}

Write-Host "完成!" -ForegroundColor Green
