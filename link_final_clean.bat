@echo off
echo Starting final TouchGFX complete project linking (clean version)...

REM Set toolchain path
set TOOLCHAIN_PATH=C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin
set GCC=%TOOLCHAIN_PATH%\arm-none-eabi-gcc.exe
set OBJCOPY=%TOOLCHAIN_PATH%\arm-none-eabi-objcopy.exe
set SIZE=%TOOLCHAIN_PATH%\arm-none-eabi-size.exe

echo Using toolchain: %TOOLCHAIN_PATH%

REM Compile stub file
echo Compiling stub file...
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -O2 -c touchgfx_stubs.cpp -o build_touchgfx_complete/touchgfx_stubs.o

if %ERRORLEVEL% neq 0 (
    echo Stub compilation failed!
    pause
    exit /b 1
)

echo Stub compilation successful!

REM Final link command with clean object list (no duplicates)
echo Starting final clean linking...
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard --specs=nano.specs --specs=nosys.specs -Wl,--gc-sections -static -Wl,--start-group -lc -lm -lstdc++ -lsupc++ -lgcc -Wl,--end-group -Tgcc/STM32H745XIHX_FLASH_CM7.ld @link_objects_final.txt build_touchgfx_complete/touchgfx_stubs.o CM7/Middlewares/ST/touchgfx/lib/core/cortex_m7/gcc/libtouchgfx-float-abi-hard.a -o build_touchgfx_complete/TouchGFX_Complete_Final.elf

if %ERRORLEVEL% equ 0 (
    echo Linking successful!
    
    REM Generate HEX and BIN files
    echo Generating HEX and BIN files...
    "%OBJCOPY%" -O ihex build_touchgfx_complete/TouchGFX_Complete_Final.elf build_touchgfx_complete/TouchGFX_Complete_Final.hex
    "%OBJCOPY%" -O binary build_touchgfx_complete/TouchGFX_Complete_Final.elf build_touchgfx_complete/TouchGFX_Complete_Final.bin
    
    if %ERRORLEVEL% equ 0 (
        echo Compilation complete!
        echo.
        echo === TouchGFX Complete Project Successfully Compiled ===
        echo Output files:
        echo   ELF: build_touchgfx_complete/TouchGFX_Complete_Final.elf
        echo   HEX: build_touchgfx_complete/TouchGFX_Complete_Final.hex
        echo   BIN: build_touchgfx_complete/TouchGFX_Complete_Final.bin
        echo.
        
        REM Show program size
        echo Program size information:
        "%SIZE%" build_touchgfx_complete/TouchGFX_Complete_Final.elf
        echo.
        
        echo === Project Features ===
        echo - STM32H745I-DISCO dual-core microcontroller support
        echo - TouchGFX 4.25.0 GUI framework with complete asset pipeline
        echo - ADC1 real-time data acquisition with 30-point averaging
        echo - DMA circular mode for efficient data transfer
        echo - FreeRTOS V10.3.1 real-time operating system
        echo - Gauge widget with ADC1-controlled pointer
        echo - Complete font rendering with kerning support
        echo - Image assets and bitmap database
        echo.
        
        set /p download="Download firmware to STM32H745I-DISCO board? (y/n): "
        if /i "%download%"=="y" (
            echo Downloading firmware to development board...
            C:\ST\STM32CubeCLT_1.18.0\STM32CubeProgrammer\bin\STM32_Programmer_CLI.exe -c port=SWD freq=4000 -w build_touchgfx_complete/TouchGFX_Complete_Final.hex -v -rst
            
            if %ERRORLEVEL% equ 0 (
                echo.
                echo === Firmware Download Successful! ===
                echo TouchGFX interface should now be displayed on the development board
                echo ADC1 input (PA1) controls the right gauge pointer
                echo Connect a potentiometer or variable voltage source to PA1 for testing
            ) else (
                echo Firmware download failed!
                echo Please check ST-LINK connection and try again
            )
        )
    ) else (
        echo HEX/BIN file generation failed!
    )
) else (
    echo Final linking failed!
    echo Please check error messages above
)

echo.
echo === TouchGFX Complete Project Build Process Finished ===
pause
