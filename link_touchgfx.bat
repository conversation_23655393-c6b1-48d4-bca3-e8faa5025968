@echo off
echo === 链接TouchGFX完整固件 ===

set TOOLCHAIN=C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin
set GCC=%TOOLCHAIN%\arm-none-eabi-gcc.exe
set OBJCOPY=%TOOLCHAIN%\arm-none-eabi-objcopy.exe
set SIZE=%TOOLCHAIN%\arm-none-eabi-size.exe

echo 链接TouchGFX完整固件...
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard --specs=nano.specs --specs=nosys.specs -Wl,--gc-sections -static --specs=nano.specs -Wl,--start-group -lc -lm -Wl,--end-group -Tgcc/STM32H745XIHX_FLASH_CM7.ld build_touchgfx_complete/*.o -o build_touchgfx_complete/TouchGFX_Complete.elf

if %ERRORLEVEL% eq 0 (
    echo 链接成功!
    
    echo 生成HEX和BIN文件...
    "%OBJCOPY%" -O ihex build_touchgfx_complete/TouchGFX_Complete.elf build_touchgfx_complete/TouchGFX_Complete.hex
    "%OBJCOPY%" -O binary build_touchgfx_complete/TouchGFX_Complete.elf build_touchgfx_complete/TouchGFX_Complete.bin
    
    echo 显示程序大小...
    "%SIZE%" build_touchgfx_complete/TouchGFX_Complete.elf
    
    echo 固件文件已生成:
    echo   ELF: build_touchgfx_complete/TouchGFX_Complete.elf
    echo   HEX: build_touchgfx_complete/TouchGFX_Complete.hex
    echo   BIN: build_touchgfx_complete/TouchGFX_Complete.bin
    
    echo.
    set /p download="是否下载到STM32H745I-DISCO开发板? (y/n): "
    if /i "%download%"=="y" (
        echo 下载固件到开发板...
        C:\ST\STM32CubeCLT_1.18.0\STM32CubeProgrammer\bin\STM32_Programmer_CLI.exe -c port=SWD freq=4000 -w build_touchgfx_complete/TouchGFX_Complete.hex -v -rst
        
        if %ERRORLEVEL% eq 0 (
            echo 固件下载成功!
            echo TouchGFX界面应该显示，ADC1(PA1)控制右侧仪表盘
        ) else (
            echo 固件下载失败!
        )
    )
) else (
    echo 链接失败!
    exit /b 1
)

echo 完成!
pause
