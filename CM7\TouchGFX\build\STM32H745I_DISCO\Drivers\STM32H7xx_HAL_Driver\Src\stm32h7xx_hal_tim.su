stm32h7xx_hal_tim.c:6990:13:TIM_OC1_SetConfig	16	static
stm32h7xx_hal_tim.c:7142:13:TIM_OC3_SetConfig	16	static
stm32h7xx_hal_tim.c:7217:13:TIM_OC4_SetConfig	12	static
stm32h7xx_hal_tim.c:7278:13:TIM_OC5_SetConfig	12	static
stm32h7xx_hal_tim.c:7332:13:TIM_OC6_SetConfig	12	static
stm32h7xx_hal_tim.c:372:13:HAL_TIM_Base_MspInit	0	static
stm32h7xx_hal_tim.c:387:13:HAL_TIM_Base_MspDeInit	0	static
stm32h7xx_hal_tim.c:329:19:HAL_TIM_Base_DeInit	8	static
stm32h7xx_hal_tim.c:403:19:HAL_TIM_Base_Start	0	static
stm32h7xx_hal_tim.c:442:19:HAL_TIM_Base_Stop	0	static
stm32h7xx_hal_tim.c:462:19:HAL_TIM_Base_Start_IT	0	static
stm32h7xx_hal_tim.c:504:19:HAL_TIM_Base_Stop_IT	0	static
stm32h7xx_hal_tim.c:529:19:HAL_TIM_Base_Start_DMA	16	static
stm32h7xx_hal_tim.c:598:19:HAL_TIM_Base_Stop_DMA	8	static
stm32h7xx_hal_tim.c:756:13:HAL_TIM_OC_MspInit	0	static
stm32h7xx_hal_tim.c:771:13:HAL_TIM_OC_MspDeInit	0	static
stm32h7xx_hal_tim.c:713:19:HAL_TIM_OC_DeInit	8	static
stm32h7xx_hal_tim.c:1425:13:HAL_TIM_PWM_MspInit	0	static
stm32h7xx_hal_tim.c:1440:13:HAL_TIM_PWM_MspDeInit	0	static
stm32h7xx_hal_tim.c:1382:19:HAL_TIM_PWM_DeInit	8	static
stm32h7xx_hal_tim.c:2093:13:HAL_TIM_IC_MspInit	0	static
stm32h7xx_hal_tim.c:2108:13:HAL_TIM_IC_MspDeInit	0	static
stm32h7xx_hal_tim.c:2050:19:HAL_TIM_IC_DeInit	8	static
stm32h7xx_hal_tim.c:2753:13:HAL_TIM_OnePulse_MspInit	0	static
stm32h7xx_hal_tim.c:2768:13:HAL_TIM_OnePulse_MspDeInit	0	static
stm32h7xx_hal_tim.c:2708:19:HAL_TIM_OnePulse_DeInit	8	static
stm32h7xx_hal_tim.c:2788:19:HAL_TIM_OnePulse_Start	8	static
stm32h7xx_hal_tim.c:2845:19:HAL_TIM_OnePulse_Stop	0	static
stm32h7xx_hal_tim.c:2888:19:HAL_TIM_OnePulse_Start_IT	8	static
stm32h7xx_hal_tim.c:2951:19:HAL_TIM_OnePulse_Stop_IT	0	static
stm32h7xx_hal_tim.c:3190:13:HAL_TIM_Encoder_MspInit	0	static
stm32h7xx_hal_tim.c:3205:13:HAL_TIM_Encoder_MspDeInit	0	static
stm32h7xx_hal_tim.c:3145:19:HAL_TIM_Encoder_DeInit	8	static
stm32h7xx_hal_tim.c:3225:19:HAL_TIM_Encoder_Start	12	static
stm32h7xx_hal_tim.c:3319:19:HAL_TIM_Encoder_Stop	8	static
stm32h7xx_hal_tim.c:3379:19:HAL_TIM_Encoder_Start_IT	12	static
stm32h7xx_hal_tim.c:3479:19:HAL_TIM_Encoder_Stop_IT	8	static
stm32h7xx_hal_tim.c:3544:19:HAL_TIM_Encoder_Start_DMA	32	static
stm32h7xx_hal_tim.c:3757:19:HAL_TIM_Encoder_Stop_DMA	16	static
stm32h7xx_hal_tim.c:4631:19:HAL_TIM_DMABurst_MultiWriteStart	24	static
stm32h7xx_hal_tim.c:4571:19:HAL_TIM_DMABurst_WriteStart	24	static
stm32h7xx_hal_tim.c:4815:19:HAL_TIM_DMABurst_WriteStop	16	static
stm32h7xx_hal_tim.c:4981:19:HAL_TIM_DMABurst_MultiReadStart	24	static
stm32h7xx_hal_tim.c:4923:19:HAL_TIM_DMABurst_ReadStart	24	static
stm32h7xx_hal_tim.c:5165:19:HAL_TIM_DMABurst_ReadStop	0	static
stm32h7xx_hal_tim.c:5249:19:HAL_TIM_GenerateEvent	0	static
stm32h7xx_hal_tim.c:5605:19:HAL_TIM_ConfigTI1Input	0	static
stm32h7xx_hal_tim.c:5720:10:HAL_TIM_ReadCapturedValue	0	static
stm32h7xx_hal_tim.c:5804:13:HAL_TIM_PeriodElapsedCallback	0	static
stm32h7xx_hal_tim.c:6860:13:TIM_DMAPeriodElapsedCplt	8	static
stm32h7xx_hal_tim.c:5819:13:HAL_TIM_PeriodElapsedHalfCpltCallback	0	static
stm32h7xx_hal_tim.c:6881:13:TIM_DMAPeriodElapsedHalfCplt	8	static
stm32h7xx_hal_tim.c:5834:13:HAL_TIM_OC_DelayElapsedCallback	0	static
stm32h7xx_hal_tim.c:5849:13:HAL_TIM_IC_CaptureCallback	0	static
stm32h7xx_hal_tim.c:6758:6:TIM_DMACaptureCplt	8	static
stm32h7xx_hal_tim.c:5864:13:HAL_TIM_IC_CaptureHalfCpltCallback	0	static
stm32h7xx_hal_tim.c:6821:6:TIM_DMACaptureHalfCplt	8	static
stm32h7xx_hal_tim.c:5879:13:HAL_TIM_PWM_PulseFinishedCallback	0	static
stm32h7xx_hal_tim.c:6660:13:TIM_DMADelayPulseCplt	8	static
stm32h7xx_hal_tim.c:5894:13:HAL_TIM_PWM_PulseFinishedHalfCpltCallback	0	static
stm32h7xx_hal_tim.c:6719:6:TIM_DMADelayPulseHalfCplt	8	static
stm32h7xx_hal_tim.c:5909:13:HAL_TIM_TriggerCallback	0	static
stm32h7xx_hal_tim.c:3834:6:HAL_TIM_IRQHandler	16	static
stm32h7xx_hal_tim.c:6897:13:TIM_DMATriggerCplt	8	static
stm32h7xx_hal_tim.c:5924:13:HAL_TIM_TriggerHalfCpltCallback	0	static
stm32h7xx_hal_tim.c:6918:13:TIM_DMATriggerHalfCplt	8	static
stm32h7xx_hal_tim.c:5939:13:HAL_TIM_ErrorCallback	0	static
stm32h7xx_hal_tim.c:6617:6:TIM_DMAError	8	static
stm32h7xx_hal_tim.c:6497:22:HAL_TIM_Base_GetState	0	static
stm32h7xx_hal_tim.c:6507:22:HAL_TIM_OC_GetState	0	static
stm32h7xx_hal_tim.c:6517:22:HAL_TIM_PWM_GetState	0	static
stm32h7xx_hal_tim.c:6527:22:HAL_TIM_IC_GetState	0	static
stm32h7xx_hal_tim.c:6537:22:HAL_TIM_OnePulse_GetState	0	static
stm32h7xx_hal_tim.c:6547:22:HAL_TIM_Encoder_GetState	0	static
stm32h7xx_hal_tim.c:6557:23:HAL_TIM_GetActiveChannel	0	static
stm32h7xx_hal_tim.c:6575:29:HAL_TIM_GetChannelState	0	static
stm32h7xx_hal_tim.c:6592:30:HAL_TIM_DMABurstState	0	static
stm32h7xx_hal_tim.c:6935:6:TIM_Base_SetConfig	0	static
stm32h7xx_hal_tim.c:269:19:HAL_TIM_Base_Init	8	static
stm32h7xx_hal_tim.c:653:19:HAL_TIM_OC_Init	8	static
stm32h7xx_hal_tim.c:1322:19:HAL_TIM_PWM_Init	8	static
stm32h7xx_hal_tim.c:1990:19:HAL_TIM_IC_Init	8	static
stm32h7xx_hal_tim.c:2639:19:HAL_TIM_OnePulse_Init	16	static
stm32h7xx_hal_tim.c:3030:19:HAL_TIM_Encoder_Init	24	static
stm32h7xx_hal_tim.c:7066:6:TIM_OC2_SetConfig	16	static
stm32h7xx_hal_tim.c:4068:19:HAL_TIM_OC_ConfigChannel	16	static
stm32h7xx_hal_tim.c:4268:19:HAL_TIM_PWM_ConfigChannel	16	static
stm32h7xx_hal_tim.c:7531:6:TIM_TI1_SetConfig	16	static
stm32h7xx_hal_tim.c:4167:19:HAL_TIM_IC_ConfigChannel	24	static
stm32h7xx_hal_tim.c:4416:19:HAL_TIM_OnePulse_ConfigChannel	56	static
stm32h7xx_hal_tim.c:7838:6:TIM_ETR_SetConfig	8	static
stm32h7xx_hal_tim.c:5288:19:HAL_TIM_ConfigOCrefClear	16	static
stm32h7xx_hal_tim.c:5446:19:HAL_TIM_ConfigClockSource	16	static
stm32h7xx_hal_tim.c:7387:26:TIM_SlaveTimer_SetConfig	16	static
stm32h7xx_hal_tim.c:5637:19:HAL_TIM_SlaveConfigSynchro	16	static
stm32h7xx_hal_tim.c:5677:19:HAL_TIM_SlaveConfigSynchro_IT	16	static
stm32h7xx_hal_tim.c:7870:6:TIM_CCxChannelCmd	8	static
stm32h7xx_hal_tim.c:794:19:HAL_TIM_OC_Start	8	static
stm32h7xx_hal_tim.c:1463:19:HAL_TIM_PWM_Start	0	static
stm32h7xx_hal_tim.c:850:19:HAL_TIM_OC_Stop	16	static
stm32h7xx_hal_tim.c:1519:19:HAL_TIM_PWM_Stop	0	static
stm32h7xx_hal_tim.c:885:19:HAL_TIM_OC_Start_IT	8	static
stm32h7xx_hal_tim.c:1554:19:HAL_TIM_PWM_Start_IT	0	static
stm32h7xx_hal_tim.c:978:19:HAL_TIM_OC_Stop_IT	16	static
stm32h7xx_hal_tim.c:1647:19:HAL_TIM_PWM_Stop_IT	0	static
stm32h7xx_hal_tim.c:1055:19:HAL_TIM_OC_Start_DMA	16	static
stm32h7xx_hal_tim.c:1724:19:HAL_TIM_PWM_Start_DMA	0	static
stm32h7xx_hal_tim.c:1219:19:HAL_TIM_OC_Stop_DMA	16	static
stm32h7xx_hal_tim.c:1887:19:HAL_TIM_PWM_Stop_DMA	0	static
stm32h7xx_hal_tim.c:2129:19:HAL_TIM_IC_Start	8	static
stm32h7xx_hal_tim.c:2181:19:HAL_TIM_IC_Stop	16	static
stm32h7xx_hal_tim.c:2211:19:HAL_TIM_IC_Start_IT	8	static
stm32h7xx_hal_tim.c:2303:19:HAL_TIM_IC_Stop_IT	16	static
stm32h7xx_hal_tim.c:2375:19:HAL_TIM_IC_Start_DMA	24	static
stm32h7xx_hal_tim.c:2534:19:HAL_TIM_IC_Stop_DMA	16	static
