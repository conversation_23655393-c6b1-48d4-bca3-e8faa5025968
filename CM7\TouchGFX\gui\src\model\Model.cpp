#include <gui/model/Model.hpp>
#include <gui/model/ModelListener.hpp>
#include <cstdint>

// 外部变量声明 - 来自main.c中的ADC数据
extern volatile uint16_t adc1_average;      // ADC1平均值
extern volatile uint8_t gauge_update_flag;  // 仪表盘更新标志

Model::Model() : modelListener(0), update_counter(0)
{

}

void Model::tick()
{
    // 增加更新计数器
    update_counter++;

    // 每UPDATE_INTERVAL个tick检查一次ADC数据
    if (update_counter >= UPDATE_INTERVAL)
    {
        update_counter = 0;

        // 检查是否有新的ADC数据需要处理
        if (gauge_update_flag)
        {
            processADC1Data();
            gauge_update_flag = 0; // 清除更新标志
        }
    }
}

/**
 * @brief 处理ADC1数据并更新仪表盘
 * @note 将ADC1的30个采样平均值转换为仪表盘数值并通知View更新
 */
void Model::processADC1Data()
{
    // 获取ADC1平均值并转换为仪表盘数值
    uint8_t gauge_value = convertADC1ToGaugeValue(adc1_average);

    // 通过ModelListener通知View更新仪表盘
    if (modelListener)
    {
        modelListener->updateGauge2Value(gauge_value);
    }
}

/**
 * @brief 将ADC1平均值转换为仪表盘数值
 * @param adc_average: ADC1的30个采样平均值
 * @retval 仪表盘数值 (0-50范围)
 */
uint8_t Model::convertADC1ToGaugeValue(uint16_t adc_average)
{
    // ADC1 16位分辨率: 0-65535 对应 0-3.3V
    // 仪表盘2范围: 0-50
    // 转换公式: gauge_value = (adc_average * 50) / 65535

    uint32_t gauge_value = ((uint32_t)adc_average * 50) / 65535;

    // 确保数值在有效范围内
    if (gauge_value > 50)
        gauge_value = 50;

    return (uint8_t)gauge_value;
}
