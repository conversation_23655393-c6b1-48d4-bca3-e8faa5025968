# STM32H745I Minimal ADC1 Build Script (without TouchGFX)
Write-Host "=== STM32H745I Minimal ADC1 Build ===" -ForegroundColor Green

# Set environment variables
$env:PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin;C:\ST\STM32CubeCLT_1.18.0\STM32_Programmer_CLI\bin;" + $env:PATH

# Define tools
$GCC = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe"
$OBJCOPY = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-objcopy.exe"
$SIZE = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-size.exe"
$STLINK = "C:\ST\STM32CubeCLT_1.18.0\STM32_Programmer_CLI\bin\STM32_Programmer_CLI.exe"

# Common compiler flags
$COMMON_FLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb", 
    "-mfpu=fpv5-sp-d16",
    "-mfloat-abi=hard",
    "-DUSE_HAL_DRIVER",
    "-DSTM32H745xx", 
    "-DCORE_CM7"
)

# Include paths
$INCLUDE_PATHS = @(
    "-ICM7/Core/Inc",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy",
    "-IDrivers/CMSIS/Device/ST/STM32H7xx/Include",
    "-IDrivers/CMSIS/Include",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/include",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F"
)

# Compiler flags
$CFLAGS = $COMMON_FLAGS + @("--specs=nano.specs", "-Os", "-Wall", "-fdata-sections", "-ffunction-sections", "-g3", "-std=gnu11")

# Linker flags
$LDFLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb",
    "-mfpu=fpv5-sp-d16", 
    "-mfloat-abi=hard",
    "--specs=nano.specs",
    "--specs=nosys.specs",
    "-Wl,--gc-sections",
    "-static",
    "-Wl,--start-group",
    "-lc",
    "-lm",
    "-Wl,--end-group"
)

# Create build directory
$BUILD_DIR = "build_minimal_adc"
if (Test-Path $BUILD_DIR) {
    Remove-Item -Recurse -Force $BUILD_DIR
}
New-Item -ItemType Directory -Path $BUILD_DIR | Out-Null

Write-Host "Building minimal ADC1 test application..." -ForegroundColor Yellow

# Create a minimal main.c for ADC1 testing
$MinimalMainC = @"
#include "stm32h7xx_hal.h"

// ADC1 buffer for DMA
#define ADC1_BUFFER_SIZE 30
uint16_t adc1_buffer[ADC1_BUFFER_SIZE];
volatile uint16_t adc1_average = 0;
volatile uint8_t gauge_update_flag = 0;

ADC_HandleTypeDef hadc1;
DMA_HandleTypeDef hdma_adc1;

void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_DMA_Init(void);
static void MX_ADC1_Init(void);

int main(void) {
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_DMA_Init();
    MX_ADC1_Init();
    
    // Start ADC1 with DMA
    HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adc1_buffer, ADC1_BUFFER_SIZE);
    
    while (1) {
        if (gauge_update_flag) {
            gauge_update_flag = 0;
            // ADC1 average value is ready in adc1_average
            // Map 0-4095 (12-bit) to 0-50 range
            uint16_t gauge_value = (adc1_average * 50) / 4095;
            // Here you would update the gauge display
        }
        HAL_Delay(100);
    }
}

void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc) {
    if (hadc->Instance == ADC1) {
        uint32_t sum = 0;
        for (int i = 0; i < ADC1_BUFFER_SIZE; i++) {
            sum += adc1_buffer[i];
        }
        adc1_average = sum / ADC1_BUFFER_SIZE;
        gauge_update_flag = 1;
    }
}

void SystemClock_Config(void) {
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
    
    __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);
    
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
    RCC_OscInitStruct.HSEState = RCC_HSE_ON;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
    RCC_OscInitStruct.PLL.PLLM = 5;
    RCC_OscInitStruct.PLL.PLLN = 160;
    RCC_OscInitStruct.PLL.PLLP = 2;
    RCC_OscInitStruct.PLL.PLLQ = 4;
    RCC_OscInitStruct.PLL.PLLR = 2;
    HAL_RCC_OscConfig(&RCC_OscInitStruct);
    
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK|RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
    RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;
    RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;
    HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2);
}

static void MX_ADC1_Init(void) {
    ADC_ChannelConfTypeDef sConfig = {0};
    
    hadc1.Instance = ADC1;
    hadc1.Init.ClockPrescaler = ADC_CLOCK_ASYNC_DIV1;
    hadc1.Init.Resolution = ADC_RESOLUTION_16B;
    hadc1.Init.ScanConvMode = ADC_SCAN_DISABLE;
    hadc1.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
    hadc1.Init.LowPowerAutoWait = DISABLE;
    hadc1.Init.ContinuousConvMode = ENABLE;
    hadc1.Init.NbrOfConversion = 1;
    hadc1.Init.DiscontinuousConvMode = DISABLE;
    hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;
    hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
    hadc1.Init.ConversionDataManagement = ADC_CONVERSIONDATA_DMA_CIRCULAR;
    hadc1.Init.Overrun = ADC_OVR_DATA_PRESERVED;
    hadc1.Init.LeftBitShift = ADC_LEFTBITSHIFT_NONE;
    hadc1.Init.OversamplingMode = DISABLE;
    HAL_ADC_Init(&hadc1);
    
    sConfig.Channel = ADC_CHANNEL_17;
    sConfig.Rank = ADC_REGULAR_RANK_1;
    sConfig.SamplingTime = ADC_SAMPLETIME_1CYCLE_5;
    sConfig.SingleDiff = ADC_SINGLE_ENDED;
    sConfig.OffsetNumber = ADC_OFFSET_NONE;
    sConfig.Offset = 0;
    HAL_ADC_ConfigChannel(&hadc1, &sConfig);
}

static void MX_DMA_Init(void) {
    __HAL_RCC_DMA1_CLK_ENABLE();
    
    hdma_adc1.Instance = DMA1_Stream1;
    hdma_adc1.Init.Request = DMA_REQUEST_ADC1;
    hdma_adc1.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_adc1.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_adc1.Init.MemInc = DMA_MINC_ENABLE;
    hdma_adc1.Init.PeriphDataAlignment = DMA_PDATAALIGN_HALFWORD;
    hdma_adc1.Init.MemDataAlignment = DMA_MDATAALIGN_HALFWORD;
    hdma_adc1.Init.Mode = DMA_CIRCULAR;
    hdma_adc1.Init.Priority = DMA_PRIORITY_LOW;
    hdma_adc1.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    HAL_DMA_Init(&hdma_adc1);
    
    __HAL_LINKDMA(&hadc1, DMA_Handle, hdma_adc1);
    
    HAL_NVIC_SetPriority(DMA1_Stream1_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(DMA1_Stream1_IRQn);
}

static void MX_GPIO_Init(void) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOH_CLK_ENABLE();
    
    GPIO_InitStruct.Pin = GPIO_PIN_1;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
}

void DMA1_Stream1_IRQHandler(void) {
    HAL_DMA_IRQHandler(&hdma_adc1);
}

void HAL_MspInit(void) {
    __HAL_RCC_SYSCFG_CLK_ENABLE();
}

void HAL_ADC_MspInit(ADC_HandleTypeDef* hadc) {
    if(hadc->Instance==ADC1) {
        __HAL_RCC_ADC12_CLK_ENABLE();
    }
}

void Error_Handler(void) {
    while(1) {}
}
"@

# Write minimal main.c
$MinimalMainC | Out-File -FilePath "$BUILD_DIR/main_minimal.c" -Encoding UTF8

Write-Host "Created minimal ADC1 test application" -ForegroundColor Green

# Minimal source files
$MinimalFiles = @(
    @("gcc/startup_stm32h745xihx_cm7.s", "startup.o", "ASM"),
    @("Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.c", "system_stm32h7xx.o", "C"),
    @("$BUILD_DIR/main_minimal.c", "main_minimal.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c", "stm32h7xx_hal.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c", "stm32h7xx_hal_adc.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c", "stm32h7xx_hal_adc_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c", "stm32h7xx_hal_cortex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c", "stm32h7xx_hal_dma.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c", "stm32h7xx_hal_gpio.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c", "stm32h7xx_hal_rcc.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c", "stm32h7xx_hal_rcc_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c", "stm32h7xx_hal_pwr.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c", "stm32h7xx_hal_pwr_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c", "stm32h7xx_hal_flash.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c", "stm32h7xx_hal_flash_ex.o", "C")
)

$ObjectFiles = @()
$SuccessCount = 0
$ErrorCount = 0

Write-Host "`n=== Compiling Minimal Files ===" -ForegroundColor Cyan

foreach ($file in $MinimalFiles) {
    $SourceFile = $file[0]
    $OutputFile = $file[1]
    $FileType = $file[2]
    
    if (!(Test-Path $SourceFile)) {
        Write-Host "File not found: $SourceFile" -ForegroundColor Yellow
        $ErrorCount++
        continue
    }
    
    Write-Host "Compiling $FileType`: $SourceFile"
    $cmd_args = $CFLAGS + $INCLUDE_PATHS + @("-c", $SourceFile, "-o", "$BUILD_DIR/$OutputFile")
    
    & $GCC @cmd_args 2>&1 | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  SUCCESS: $OutputFile" -ForegroundColor Green
        $ObjectFiles += "$BUILD_DIR/$OutputFile"
        $SuccessCount++
    } else {
        Write-Host "  FAILED: $SourceFile" -ForegroundColor Red
        $ErrorCount++
    }
}

Write-Host "`n=== Compilation Summary ===" -ForegroundColor Cyan
Write-Host "Successful: $SuccessCount files" -ForegroundColor Green
Write-Host "Failed: $ErrorCount files" -ForegroundColor Red

if ($ErrorCount -gt 3) {
    Write-Host "Too many compilation errors!" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== Linking ===" -ForegroundColor Cyan
Write-Host "Linking minimal ADC1 executable..."

$LinkerScript = "gcc/STM32H745XIHX_FLASH_CM7.ld"
if (!(Test-Path $LinkerScript)) {
    Write-Host "Linker script not found: $LinkerScript" -ForegroundColor Red
    exit 1
}

Write-Host "Using linker script: $LinkerScript"

# Link
$link_args = $LDFLAGS + @("-T$LinkerScript") + $ObjectFiles + @("-o", "$BUILD_DIR/ADC1_Test.elf")
& $GCC @link_args

if ($LASTEXITCODE -eq 0) {
    Write-Host "SUCCESS: Linking completed" -ForegroundColor Green
} else {
    Write-Host "FAILED: Linking failed" -ForegroundColor Red
    exit 1
}

# Generate binary and hex files
Write-Host "`n=== Generating Binary Files ===" -ForegroundColor Cyan
& $OBJCOPY -O binary "$BUILD_DIR/ADC1_Test.elf" "$BUILD_DIR/ADC1_Test.bin"
& $OBJCOPY -O ihex "$BUILD_DIR/ADC1_Test.elf" "$BUILD_DIR/ADC1_Test.hex"

# Show size
Write-Host "`n=== Memory Usage ===" -ForegroundColor Cyan
& $SIZE "$BUILD_DIR/ADC1_Test.elf"

Write-Host "`n=== Flashing with ST-LINK ===" -ForegroundColor Cyan
Write-Host "Connecting to STM32H745I-DISCO..."

# Flash the firmware
$flash_args = @("-c", "port=SWD", "freq=4000", "-w", "$BUILD_DIR/ADC1_Test.hex", "-v", "-rst")
& $STLINK @flash_args

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nSUCCESS: ADC1 test firmware flashed successfully!" -ForegroundColor Green
    Write-Host "ADC1 is now reading PA1 (Channel 17) with 30-sample averaging" -ForegroundColor Green
    Write-Host "Connect a voltage source (0-3.3V) to PA1 to test ADC functionality" -ForegroundColor Cyan
} else {
    Write-Host "`nFAILED: Flash operation failed" -ForegroundColor Red
    Write-Host "Please check ST-LINK connection and try again" -ForegroundColor Yellow
}

Write-Host "`nMinimal ADC1 build and flash completed!" -ForegroundColor Cyan
