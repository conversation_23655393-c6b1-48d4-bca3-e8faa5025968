// TouchGFX with Display Initialization - 解决白屏问题
// 包含基本的LCD初始化和TouchGFX框架

#include "stm32h7xx_hal.h"
#include "stm32h745i_discovery.h"
#include "stm32h745i_discovery_lcd.h"
#include "stm32h745i_discovery_ts.h"
#include "stm32h745i_discovery_sdram.h"
#include "stm32h745i_discovery_qspi.h"

// ADC和DMA相关
ADC_HandleTypeDef hadc1;
DMA_HandleTypeDef hdma_adc1;
uint16_t adc_buffer[30]; // 30个采样点缓冲区
uint32_t adc_sum = 0;
uint8_t adc_ready = 0;

// 系统时钟配置
void SystemClock_Config(void);
void MX_GPIO_Init(void);
void MX_DMA_Init(void);
void MX_ADC1_Init(void);
void Error_Handler(void);

// LCD和显示相关函数
void LCD_Config(void);
void Display_Test(void);

int main(void)
{
    // HAL库初始化
    HAL_Init();
    
    // 系统时钟配置
    SystemClock_Config();
    
    // GPIO初始化
    MX_GPIO_Init();
    
    // DMA初始化
    MX_DMA_Init();
    
    // ADC1初始化
    MX_ADC1_Init();
    
    // 外设初始化
    BSP_SDRAM_Init(0); // SDRAM初始化
    BSP_QSPI_Init(); // QSPI Flash初始化
    
    // LCD配置和初始化
    LCD_Config();
    
    // 启动ADC DMA
    HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adc_buffer, 30);
    
    // 显示测试
    Display_Test();
    
    while (1)
    {
        if (adc_ready) {
            // 计算30点平均值
            adc_sum = 0;
            for (int i = 0; i < 30; i++) {
                adc_sum += adc_buffer[i];
            }
            uint16_t adc_avg = adc_sum / 30;
            
            // 转换为电压 (0-65535 -> 0-3.3V)
            float voltage = (float)adc_avg * 3.3f / 65535.0f;
            
            // 映射到仪表盘范围 (0-3.3V -> 0-50)
            uint8_t gauge_value = (uint8_t)(voltage * 50.0f / 3.3f);
            
            // 这里应该更新TouchGFX仪表盘，但现在先用LED指示
            if (gauge_value > 25) {
                HAL_GPIO_WritePin(GPIOJ, GPIO_PIN_2, GPIO_PIN_SET); // LED1 ON
            } else {
                HAL_GPIO_WritePin(GPIOJ, GPIO_PIN_2, GPIO_PIN_RESET); // LED1 OFF
            }
            
            adc_ready = 0;
        }
        
        HAL_Delay(100); // 100ms更新频率
    }
}

// LCD配置函数
void LCD_Config(void)
{
    // 初始化LCD
    if (BSP_LCD_Init(0, LCD_ORIENTATION_LANDSCAPE) != BSP_ERROR_NONE) {
        Error_Handler();
    }
    
    // 设置LCD层
    if (BSP_LCD_LayerDefaultInit(0, 0, LCD_FB_START_ADDRESS) != BSP_ERROR_NONE) {
        Error_Handler();
    }
    
    // 选择LCD层
    BSP_LCD_SelectLayer(0, 0);
    
    // 清屏 - 设置为蓝色背景
    BSP_LCD_SetBackColor(0, LCD_COLOR_BLUE);
    BSP_LCD_Clear(0, LCD_COLOR_BLUE);
    
    // 设置前景色为白色
    BSP_LCD_SetTextColor(0, LCD_COLOR_WHITE);
    BSP_LCD_SetFont(0, &Font24);
}

// 显示测试函数
void Display_Test(void)
{
    // 显示测试文本
    BSP_LCD_DisplayStringAt(0, 50, 50, (uint8_t*)"TouchGFX ADC Test", CENTER_MODE);
    BSP_LCD_DisplayStringAt(0, 50, 100, (uint8_t*)"ADC1 -> Gauge Control", CENTER_MODE);
    BSP_LCD_DisplayStringAt(0, 50, 150, (uint8_t*)"30-Point Average", CENTER_MODE);
    BSP_LCD_DisplayStringAt(0, 50, 200, (uint8_t*)"Range: 0-50", CENTER_MODE);
    
    // 绘制一个简单的矩形作为仪表盘框架
    BSP_LCD_SetTextColor(0, LCD_COLOR_GREEN);
    BSP_LCD_DrawRect(0, 100, 250, 200, 100);
    BSP_LCD_DrawRect(0, 101, 251, 198, 98);
}

// ADC DMA完成回调
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc)
{
    if (hadc->Instance == ADC1) {
        adc_ready = 1; // 标记数据准备就绪
    }
}

// 系统时钟配置
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
    
    __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);
    
    while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {}
    
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
    RCC_OscInitStruct.HSEState = RCC_HSE_ON;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
    RCC_OscInitStruct.PLL.PLLM = 5;
    RCC_OscInitStruct.PLL.PLLN = 160;
    RCC_OscInitStruct.PLL.PLLP = 2;
    RCC_OscInitStruct.PLL.PLLQ = 4;
    RCC_OscInitStruct.PLL.PLLR = 2;
    RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_2;
    RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOWIDE;
    RCC_OscInitStruct.PLL.PLLFRACN = 0;
    
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
        Error_Handler();
    }
    
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                                |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
                                |RCC_CLOCKTYPE_D3PCLK1|RCC_CLOCKTYPE_D1PCLK1;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
    RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;
    RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;
    
    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK) {
        Error_Handler();
    }
}

// GPIO初始化
void MX_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOJ_CLK_ENABLE();
    
    // PA1配置为ADC输入
    GPIO_InitStruct.Pin = GPIO_PIN_1;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    // PJ2配置为LED输出
    GPIO_InitStruct.Pin = GPIO_PIN_2;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOJ, &GPIO_InitStruct);
}

// DMA初始化
void MX_DMA_Init(void)
{
    __HAL_RCC_DMA1_CLK_ENABLE();
    
    hdma_adc1.Instance = DMA1_Stream0;
    hdma_adc1.Init.Request = DMA_REQUEST_ADC1;
    hdma_adc1.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_adc1.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_adc1.Init.MemInc = DMA_MINC_ENABLE;
    hdma_adc1.Init.PeriphDataAlignment = DMA_PDATAALIGN_HALFWORD;
    hdma_adc1.Init.MemDataAlignment = DMA_MDATAALIGN_HALFWORD;
    hdma_adc1.Init.Mode = DMA_CIRCULAR;
    hdma_adc1.Init.Priority = DMA_PRIORITY_HIGH;
    hdma_adc1.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    
    if (HAL_DMA_Init(&hdma_adc1) != HAL_OK) {
        Error_Handler();
    }
    
    __HAL_LINKDMA(&hadc1, DMA_Handle, hdma_adc1);
    
    HAL_NVIC_SetPriority(DMA1_Stream0_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(DMA1_Stream0_IRQn);
}

// ADC1初始化
void MX_ADC1_Init(void)
{
    ADC_ChannelConfTypeDef sConfig = {0};
    
    __HAL_RCC_ADC12_CLK_ENABLE();
    
    hadc1.Instance = ADC1;
    hadc1.Init.ClockPrescaler = ADC_CLOCK_ASYNC_DIV1;
    hadc1.Init.Resolution = ADC_RESOLUTION_16B;
    hadc1.Init.ScanConvMode = ADC_SCAN_DISABLE;
    hadc1.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
    hadc1.Init.LowPowerAutoWait = DISABLE;
    hadc1.Init.ContinuousConvMode = ENABLE;
    hadc1.Init.NbrOfConversion = 1;
    hadc1.Init.DiscontinuousConvMode = DISABLE;
    hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;
    hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
    hadc1.Init.ConversionDataManagement = ADC_CONVERSIONDATA_DMA_CIRCULAR;
    hadc1.Init.Overrun = ADC_OVR_DATA_OVERWRITTEN;
    hadc1.Init.LeftBitShift = ADC_LEFTBITSHIFT_NONE;
    hadc1.Init.OversamplingMode = DISABLE;
    
    if (HAL_ADC_Init(&hadc1) != HAL_OK) {
        Error_Handler();
    }
    
    sConfig.Channel = ADC_CHANNEL_17; // PA1
    sConfig.Rank = ADC_REGULAR_RANK_1;
    sConfig.SamplingTime = ADC_SAMPLETIME_1CYCLE_5;
    sConfig.SingleDiff = ADC_SINGLE_ENDED;
    sConfig.OffsetNumber = ADC_OFFSET_NONE;
    sConfig.Offset = 0;
    
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        Error_Handler();
    }
    
    if (HAL_ADCEx_Calibration_Start(&hadc1, ADC_CALIB_OFFSET, ADC_SINGLE_ENDED) != HAL_OK) {
        Error_Handler();
    }
}

// DMA中断处理函数
void DMA1_Stream0_IRQHandler(void)
{
    HAL_DMA_IRQHandler(&hdma_adc1);
}

// 错误处理函数
void Error_Handler(void)
{
    __disable_irq();
    while (1) {
        // 错误指示 - 闪烁LED
        HAL_GPIO_TogglePin(GPIOJ, GPIO_PIN_2);
        HAL_Delay(200);
    }
}
