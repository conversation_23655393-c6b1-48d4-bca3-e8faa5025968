stm32h7xx_hal_mdma.c:1760:13:MDMA_SetConfig	12	static
stm32h7xx_hal_mdma.c:218:19:H<PERSON>_MDMA_Init	16	static
stm32h7xx_hal_mdma.c:294:19:H<PERSON>_MDMA_DeInit	0	static
stm32h7xx_hal_mdma.c:347:19:HAL_MDMA_ConfigPostRequestMask	12	static
stm32h7xx_hal_mdma.c:409:19:HAL_MDMA_RegisterCallback	8	static
stm32h7xx_hal_mdma.c:474:19:HAL_MDMA_UnRegisterCallback	0	static
stm32h7xx_hal_mdma.c:568:19:H<PERSON>_MDMA_LinkedList_CreateNode	8	static
stm32h7xx_hal_mdma.c:711:19:HAL_MDMA_LinkedList_AddNode	20	static
stm32h7xx_hal_mdma.c:844:19:HAL_MDMA_LinkedList_RemoveNode	16	static
stm32h7xx_hal_mdma.c:957:19:HAL_MDMA_LinkedList_EnableCircularMode	8	static
stm32h7xx_hal_mdma.c:1001:19:HAL_MDMA_LinkedList_DisableCircularMode	0	static
stm32h7xx_hal_mdma.c:1072:19:HAL_MDMA_Start	32	static
stm32h7xx_hal_mdma.c:1132:19:HAL_MDMA_Start_IT	24	static
stm32h7xx_hal_mdma.c:1215:19:HAL_MDMA_Abort	16	static
stm32h7xx_hal_mdma.c:1280:19:HAL_MDMA_Abort_IT	0	static
stm32h7xx_hal_mdma.c:1315:19:HAL_MDMA_PollForTransfer	24	static
stm32h7xx_hal_mdma.c:1462:19:HAL_MDMA_GenerateSWRequest	0	static
stm32h7xx_hal_mdma.c:1504:6:HAL_MDMA_IRQHandler	24	static
stm32h7xx_hal_mdma.c:1722:23:HAL_MDMA_GetState	0	static
stm32h7xx_hal_mdma.c:1733:10:HAL_MDMA_GetError	0	static
