# 测试编译单个文件
Write-Host "Testing compilation of main.c..." -ForegroundColor Yellow

$GCC = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe"

if (!(Test-Path $GCC)) {
    Write-Host "GCC not found!" -ForegroundColor Red
    exit 1
}

# 创建输出目录
if (!(Test-Path "test_build")) { New-Item -ItemType Directory -Path "test_build" | Out-Null }

Write-Host "Compiling main.c with verbose output..." -ForegroundColor Green

$cmd = @(
    $GCC,
    "-mcpu=cortex-m7",
    "-mthumb", 
    "-mfpu=fpv5-sp-d16",
    "-mfloat-abi=hard",
    "-DUSE_HAL_DRIVER",
    "-DSTM32H745xx", 
    "-DCORE_CM7",
    "-DUSE_PWR_DIRECT_SMPS_SUPPLY",
    "-Os",
    "-Wall",
    "-fdata-sections",
    "-ffunction-sections",
    "-g",
    "-std=gnu11",
    "-ICM7/Core/Inc",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy", 
    "-IDrivers/CMSIS/Device/ST/STM32H7xx/Include",
    "-IDrivers/CMSIS/Include",
    "-v",
    "-c",
    "CM7/Core/Src/main.c",
    "-o",
    "test_build/main.o"
)

Write-Host "Command: $($cmd -join ' ')" -ForegroundColor Cyan

try {
    & $cmd[0] $cmd[1..($cmd.Length-1)]
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: main.c compiled successfully!" -ForegroundColor Green
    } else {
        Write-Host "FAILED: Compilation failed with exit code $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}
