stm32h7xx_hal_qspi.c:2385:13:Q<PERSON><PERSON>_DMARxCplt	0	static
stm32h7xx_hal_qspi.c:2399:13:QSPI_DMATxCplt	0	static
stm32h7xx_hal_qspi.c:2511:13:Q<PERSON><PERSON>_Config	32	static
stm32h7xx_hal_qspi.c:2478:26:Q<PERSON><PERSON>_WaitFlagStateUntilTimeout	24	static
stm32h7xx_hal_qspi.c:438:13:HAL_QSPI_MspInit	0	static
stm32h7xx_hal_qspi.c:306:19:HAL_QSPI_Init	24	static
stm32h7xx_hal_qspi.c:453:13:HAL_QSPI_MspDeInit	0	static
stm32h7xx_hal_qspi.c:400:19:HAL_QSPI_DeInit	8	static
stm32h7xx_hal_qspi.c:784:19:HAL_QSPI_Command	40	static
stm32h7xx_hal_qspi.c:873:19:HAL_QSPI_Command_IT	32	static
stm32h7xx_hal_qspi.c:973:19:HAL_QSPI_Transmit	32	static
stm32h7xx_hal_qspi.c:1056:19:HAL_QSPI_Receive	32	static
stm32h7xx_hal_qspi.c:1141:19:HAL_QSPI_Transmit_IT	8	static
stm32h7xx_hal_qspi.c:1201:19:HAL_QSPI_Receive_IT	12	static
stm32h7xx_hal_qspi.c:1265:19:HAL_QSPI_Transmit_DMA	32	static
stm32h7xx_hal_qspi.c:1378:19:HAL_QSPI_Receive_DMA	32	static
stm32h7xx_hal_qspi.c:1495:19:HAL_QSPI_AutoPolling	40	static
stm32h7xx_hal_qspi.c:1595:19:HAL_QSPI_AutoPolling_IT	32	static
stm32h7xx_hal_qspi.c:1699:19:HAL_QSPI_MemoryMapped	32	static
stm32h7xx_hal_qspi.c:1785:13:HAL_QSPI_ErrorCallback	0	static
stm32h7xx_hal_qspi.c:2435:13:QSPI_DMAAbortCplt	8	static
stm32h7xx_hal_qspi.c:1800:13:HAL_QSPI_AbortCpltCallback	0	static
stm32h7xx_hal_qspi.c:1815:13:HAL_QSPI_CmdCpltCallback	0	static
stm32h7xx_hal_qspi.c:1830:13:HAL_QSPI_RxCpltCallback	0	static
stm32h7xx_hal_qspi.c:1845:13:HAL_QSPI_TxCpltCallback	0	static
stm32h7xx_hal_qspi.c:1861:13:HAL_QSPI_FifoThresholdCallback	0	static
stm32h7xx_hal_qspi.c:1876:13:HAL_QSPI_StatusMatchCallback	0	static
stm32h7xx_hal_qspi.c:1891:13:HAL_QSPI_TimeOutCallback	0	static
stm32h7xx_hal_qspi.c:492:6:HAL_QSPI_IRQHandler	16	static
stm32h7xx_hal_qspi.c:2133:23:HAL_QSPI_GetState	0	static
stm32h7xx_hal_qspi.c:2144:10:HAL_QSPI_GetError	0	static
stm32h7xx_hal_qspi.c:2154:19:HAL_QSPI_Abort	24	static
stm32h7xx_hal_qspi.c:2219:19:HAL_QSPI_Abort_IT	8	static
stm32h7xx_hal_qspi.c:2413:13:QSPI_DMAError	0	static
stm32h7xx_hal_qspi.c:2284:6:HAL_QSPI_SetTimeout	0	static
stm32h7xx_hal_qspi.c:2294:19:HAL_QSPI_SetFifoThreshold	8	static
stm32h7xx_hal_qspi.c:2326:10:HAL_QSPI_GetFifoThreshold	0	static
stm32h7xx_hal_qspi.c:2338:19:HAL_QSPI_SetFlashID	0	static
