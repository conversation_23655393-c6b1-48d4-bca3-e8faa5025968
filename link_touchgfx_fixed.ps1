# 修正的TouchGFX链接脚本
# 解决C++运行时库和缺失符号问题

Write-Host "开始修正TouchGFX项目链接..." -ForegroundColor Green

# 工具链路径 - 使用相对路径查找
$POSSIBLE_PATHS = @(
    "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin",
    "C:\Program Files\STMicroelectronics\STM32Cube\STM32CubeProgrammer\bin",
    "C:\ST\STM32CubeCLT_1.16.0\GNU-tools-for-STM32\bin"
)

$TOOLCHAIN_PATH = $null
foreach ($path in $POSSIBLE_PATHS) {
    if (Test-Path "$path\arm-none-eabi-gcc.exe") {
        $TOOLCHAIN_PATH = $path
        break
    }
}

if (-not $TOOLCHAIN_PATH) {
    Write-Host "错误: 找不到STM32工具链!" -ForegroundColor Red
    Write-Host "请确保STM32CubeCLT已正确安装" -ForegroundColor Yellow
    exit 1
}

Write-Host "使用工具链: $TOOLCHAIN_PATH" -ForegroundColor Cyan

$GCC = "$TOOLCHAIN_PATH\arm-none-eabi-gcc.exe"
$OBJCOPY = "$TOOLCHAIN_PATH\arm-none-eabi-objcopy.exe"

# 创建缺失符号的存根文件
$STUB_CODE = @"
// C++运行时库存根实现
extern "C" {
    void operator delete(void* ptr, unsigned int size) {
        // 简单的删除操作，在嵌入式系统中通常为空
        (void)ptr; (void)size;
    }
    
    int __cxa_guard_acquire(long long* guard) {
        // 静态初始化保护
        return 1;
    }
    
    void __cxa_guard_release(long long* guard) {
        // 静态初始化保护释放
        (void)guard;
    }
    
    void __cxa_pure_virtual() {
        // 纯虚函数调用处理
        while(1); // 停止执行
    }
    
    int* __errno() {
        static int errno_val = 0;
        return &errno_val;
    }
    
    // BSP I2C存根函数
    int BSP_I2C4_Init(void) { return 0; }
    int BSP_I2C4_DeInit(void) { return 0; }
    int BSP_I2C4_ReadReg(unsigned short DevAddr, unsigned short Reg, unsigned char* pData, unsigned short Length) { return 0; }
    int BSP_I2C4_WriteReg(unsigned short DevAddr, unsigned short Reg, unsigned char* pData, unsigned short Length) { return 0; }
    unsigned int BSP_GetTick(void) { return 0; }
    
    // DMA2D存根函数
    void DMA2D_CropBuffer(void) {}
    void DMA2D_CopyBuffer(void) {}
    void DMA2D_CopyBufferEnd(void) {}
    void DMA2D_ExternalJobCompleted(void) {}
    
    // JPEG存根函数
    unsigned int JPEG_OUT_Read_BufferIndex = 0;
    void* Jpeg_OUT_BufferTab = 0;
    
    // FrontendApplication构造函数存根
    void _ZN19FrontendApplicationC1ER5ModelR12FrontendHeap(void) {}
    
    // VectorFontRenderer存根
    void _ZN8touchgfx22VectorFontRendererImpl20getVectorFontBuffersERPfRiRPhS4_(void) {}
    
    // HardwareMJPEGDecoder构造函数存根
    void _ZN20HardwareMJPEGDecoderC1Ev(void) {}
}

// 缺失的图像资源存根
extern "C" {
    const unsigned char image_vc1[] = {0};
    const unsigned char image_vc2[] = {0};
    const unsigned char image_vc3[] = {0};
    const unsigned char image_vc4[] = {0};
    const unsigned char image_vc5[] = {0};
    const unsigned char image_vc6[] = {0};
    const unsigned char image_vc7[] = {0};
    const unsigned char image_vc8[] = {0};
    
    // 字体kerning数据存根
    const unsigned char kerning_verdana_10_4bpp[] = {0};
    const unsigned char kerning_verdana_20_4bpp[] = {0};
    const unsigned char kerning_verdana_40_4bpp[] = {0};
}
"@

# 创建存根文件
Write-Host "创建缺失符号存根文件..." -ForegroundColor Yellow
$STUB_CODE | Out-File -FilePath "touchgfx_stubs.cpp" -Encoding UTF8

# 编译存根文件
Write-Host "编译存根文件..." -ForegroundColor Yellow
$stub_cmd = @(
    $GCC,
    "-mcpu=cortex-m7",
    "-mthumb", 
    "-mfpu=fpv5-sp-d16",
    "-mfloat-abi=hard",
    "-O2",
    "-c",
    "touchgfx_stubs.cpp",
    "-o",
    "build_touchgfx_complete/touchgfx_stubs.o"
)

& $stub_cmd[0] $stub_cmd[1..($stub_cmd.Length-1)]

if ($LASTEXITCODE -ne 0) {
    Write-Host "存根文件编译失败!" -ForegroundColor Red
    exit 1
}

# 修正的链接命令
Write-Host "开始链接..." -ForegroundColor Yellow

$link_cmd = @(
    $GCC,
    "-mcpu=cortex-m7",
    "-mthumb",
    "-mfpu=fpv5-sp-d16", 
    "-mfloat-abi=hard",
    "--specs=nano.specs",
    "--specs=nosys.specs",
    "-Wl,--gc-sections",
    "-static",
    "-Wl,--start-group",
    "-lc",
    "-lm",
    "-lstdc++",
    "-lsupc++",
    "-lgcc",
    "-Wl,--end-group",
    "-Tgcc/STM32H745XIHX_FLASH_CM7.ld",
    "@link_objects.txt",
    "build_touchgfx_complete/touchgfx_stubs.o",
    "CM7/Middlewares/ST/touchgfx/lib/core/cortex_m7/gcc/libtouchgfx-float-abi-hard.a",
    "-o",
    "build_touchgfx_complete/TouchGFX_Complete_Fixed.elf"
)

& $link_cmd[0] $link_cmd[1..($link_cmd.Length-1)]

if ($LASTEXITCODE -eq 0) {
    Write-Host "链接成功!" -ForegroundColor Green
    
    # 生成HEX文件
    Write-Host "生成HEX文件..." -ForegroundColor Yellow
    & $OBJCOPY -O ihex "build_touchgfx_complete/TouchGFX_Complete_Fixed.elf" "build_touchgfx_complete/TouchGFX_Complete_Fixed.hex"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "编译完成!" -ForegroundColor Green
        Write-Host "输出文件:" -ForegroundColor White
        Write-Host "  ELF: build_touchgfx_complete/TouchGFX_Complete_Fixed.elf" -ForegroundColor Cyan
        Write-Host "  HEX: build_touchgfx_complete/TouchGFX_Complete_Fixed.hex" -ForegroundColor Cyan
        
        # 显示文件大小
        if (Test-Path "build_touchgfx_complete/TouchGFX_Complete_Fixed.elf") {
            $fileSize = (Get-Item "build_touchgfx_complete/TouchGFX_Complete_Fixed.elf").Length
            Write-Host "ELF文件大小: $([math]::Round($fileSize/1024, 2)) KB" -ForegroundColor Green
        }
        
        Write-Host "`n项目编译成功! 可以使用STM32CubeProgrammer下载到开发板" -ForegroundColor Green
    } else {
        Write-Host "HEX文件生成失败!" -ForegroundColor Red
    }
} else {
    Write-Host "链接失败!" -ForegroundColor Red
    Write-Host "请检查错误信息并修正" -ForegroundColor Yellow
}
