# 直接编译TouchGFX项目
# 我会按照要求完成任务

Write-Host "Building TouchGFX project directly..." -ForegroundColor Yellow

# 直接设置工具路径
$GCC = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe"
$GPP = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-g++.exe"
$OBJCOPY = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-objcopy.exe"
$SIZE = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-size.exe"

# 检查工具链
if (!(Test-Path $GCC)) {
    Write-Host "Error: GCC not found at $GCC" -ForegroundColor Red
    exit 1
}

Write-Host "GCC found: $GCC" -ForegroundColor Green

# 创建输出目录
$BUILD_DIR = "build_direct"
if (Test-Path $BUILD_DIR) { Remove-Item $BUILD_DIR -Recurse -Force }
New-Item -ItemType Directory -Path $BUILD_DIR | Out-Null

# 编译器标志
$CFLAGS = "-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_PWR_DIRECT_SMPS_SUPPLY -Os -Wall -fdata-sections -ffunction-sections -g -std=gnu11"

$CPPFLAGS = "-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_PWR_DIRECT_SMPS_SUPPLY -Os -Wall -fdata-sections -ffunction-sections -g -std=c++11 -fno-exceptions -fno-rtti"

# 包含路径
$INCLUDES = "-ICM7/Core/Inc -ICM7/TouchGFX/App -ICM7/TouchGFX/target/generated -ICM7/TouchGFX/target -ICM7/TouchGFX/gui/include -ICM7/TouchGFX/generated/gui_generated/include -ICM7/TouchGFX/generated/images/include -ICM7/TouchGFX/generated/texts/include -ICM7/TouchGFX/generated/fonts/include -ICM7/Middlewares/ST/touchgfx/framework/include -ICM7/Middlewares/Third_Party/FreeRTOS/Source/include -ICM7/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -ICM7/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1 -IDrivers/STM32H7xx_HAL_Driver/Inc -IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy -IDrivers/CMSIS/Device/ST/STM32H7xx/Include -IDrivers/CMSIS/Include -IDrivers/BSP/STM32H745I-DISCO -IDrivers/BSP/Components/Common -IDrivers/BSP/Components/otm8009a -IDrivers/BSP/Components/ft5336"

Write-Host "Compiling startup file..." -ForegroundColor Green
& $GCC $CFLAGS $INCLUDES -c "gcc/startup_stm32h745xihx_cm7.s" -o "$BUILD_DIR/startup.o"
if ($LASTEXITCODE -ne 0) { Write-Host "Failed to compile startup" -ForegroundColor Red; exit 1 }

Write-Host "Compiling main.c..." -ForegroundColor Green
& $GCC $CFLAGS $INCLUDES -c "CM7/Core/Src/main.c" -o "$BUILD_DIR/main.o"
if ($LASTEXITCODE -ne 0) { Write-Host "Failed to compile main.c" -ForegroundColor Red; exit 1 }

Write-Host "Compiling system files..." -ForegroundColor Green
& $GCC $CFLAGS $INCLUDES -c "CM7/Core/Src/stm32h7xx_it.c" -o "$BUILD_DIR/stm32h7xx_it.o"
& $GCC $CFLAGS $INCLUDES -c "CM7/Core/Src/stm32h7xx_hal_msp.c" -o "$BUILD_DIR/stm32h7xx_hal_msp.o"
& $GCC $CFLAGS $INCLUDES -c "CM7/Core/Src/system_stm32h7xx.c" -o "$BUILD_DIR/system_stm32h7xx.o"

Write-Host "Compiling HAL drivers..." -ForegroundColor Green
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c" -o "$BUILD_DIR/stm32h7xx_hal.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c" -o "$BUILD_DIR/stm32h7xx_hal_cortex.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c" -o "$BUILD_DIR/stm32h7xx_hal_rcc.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c" -o "$BUILD_DIR/stm32h7xx_hal_gpio.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c" -o "$BUILD_DIR/stm32h7xx_hal_pwr.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c" -o "$BUILD_DIR/stm32h7xx_hal_pwr_ex.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c" -o "$BUILD_DIR/stm32h7xx_hal_adc.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c" -o "$BUILD_DIR/stm32h7xx_hal_adc_ex.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c" -o "$BUILD_DIR/stm32h7xx_hal_dma.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.c" -o "$BUILD_DIR/stm32h7xx_hal_ltdc.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.c" -o "$BUILD_DIR/stm32h7xx_hal_dma2d.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dsi.c" -o "$BUILD_DIR/stm32h7xx_hal_dsi.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c" -o "$BUILD_DIR/stm32h7xx_hal_i2c.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c" -o "$BUILD_DIR/stm32h7xx_hal_tim.o"

Write-Host "Compiling BSP drivers..." -ForegroundColor Green
& $GCC $CFLAGS $INCLUDES -c "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery.c" -o "$BUILD_DIR/stm32h745i_discovery.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_lcd.c" -o "$BUILD_DIR/stm32h745i_discovery_lcd.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/BSP/Components/otm8009a/otm8009a.c" -o "$BUILD_DIR/otm8009a.o"
& $GCC $CFLAGS $INCLUDES -c "Drivers/BSP/Components/ft5336/ft5336.c" -o "$BUILD_DIR/ft5336.o"

Write-Host "Compiling FreeRTOS..." -ForegroundColor Green
& $GCC $CFLAGS $INCLUDES -c "CM7/Middlewares/Third_Party/FreeRTOS/Source/tasks.c" -o "$BUILD_DIR/tasks.o"
& $GCC $CFLAGS $INCLUDES -c "CM7/Middlewares/Third_Party/FreeRTOS/Source/queue.c" -o "$BUILD_DIR/queue.o"
& $GCC $CFLAGS $INCLUDES -c "CM7/Middlewares/Third_Party/FreeRTOS/Source/list.c" -o "$BUILD_DIR/list.o"
& $GCC $CFLAGS $INCLUDES -c "CM7/Middlewares/Third_Party/FreeRTOS/Source/timers.c" -o "$BUILD_DIR/timers.o"
& $GCC $CFLAGS $INCLUDES -c "CM7/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c" -o "$BUILD_DIR/cmsis_os2.o"
& $GCC $CFLAGS $INCLUDES -c "CM7/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1/port.c" -o "$BUILD_DIR/port.o"
& $GCC $CFLAGS $INCLUDES -c "CM7/Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c" -o "$BUILD_DIR/heap_4.o"

Write-Host "Compiling TouchGFX app..." -ForegroundColor Green
& $GCC $CFLAGS $INCLUDES -c "CM7/TouchGFX/App/app_touchgfx.c" -o "$BUILD_DIR/app_touchgfx.o"

Write-Host "Linking..." -ForegroundColor Green

# 收集所有目标文件
$OBJECTS = Get-ChildItem "$BUILD_DIR/*.o" | ForEach-Object { $_.FullName }

# 链接标志
$LDFLAGS = "-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -specs=nano.specs -T gcc/STM32H745XIHX_FLASH_CM7.ld -Wl,--gc-sections -Wl,--print-memory-usage"

# 链接
& $GCC $LDFLAGS $OBJECTS -lc -lm -lnosys -o "$BUILD_DIR/TouchGFX_Direct.elf"

if ($LASTEXITCODE -ne 0) {
    Write-Host "FAILED: Linking failed" -ForegroundColor Red
    exit 1
}

Write-Host "Creating HEX file..." -ForegroundColor Green
& $OBJCOPY -O ihex "$BUILD_DIR/TouchGFX_Direct.elf" "$BUILD_DIR/TouchGFX_Direct.hex"

Write-Host "Checking size..." -ForegroundColor Green
& $SIZE "$BUILD_DIR/TouchGFX_Direct.elf"

Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "Output files:" -ForegroundColor Yellow
Write-Host "  ELF: $BUILD_DIR/TouchGFX_Direct.elf" -ForegroundColor White
Write-Host "  HEX: $BUILD_DIR/TouchGFX_Direct.hex" -ForegroundColor White

Write-Host "`nReady to flash to board!" -ForegroundColor Cyan
