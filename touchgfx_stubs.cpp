// C++运行时库存根实现
extern "C" {
    void operator delete(void* ptr, unsigned int size) {
        // 简单的删除操作，在嵌入式系统中通常为空
        (void)ptr; (void)size;
    }
    
    int __cxa_guard_acquire(long long* guard) {
        // 静态初始化保护
        return 1;
    }
    
    void __cxa_guard_release(long long* guard) {
        // 静态初始化保护释放
        (void)guard;
    }

    void __cxa_guard_abort(long long* guard) {
        // 静态初始化保护中止
        (void)guard;
    }

    void __cxa_end_cleanup() {
        // C++异常处理清理结束
    }

    void __gxx_personality_v0() {
        // C++异常处理个性化函数
    }
    
    void __cxa_pure_virtual() {
        // 纯虚函数调用处理
        while(1); // 停止执行
    }
    
    int* __errno() {
        static int errno_val = 0;
        return &errno_val;
    }
    
    // BSP I2C存根函数
    int BSP_I2C4_Init(void) { return 0; }
    int BSP_I2C4_DeInit(void) { return 0; }
    int BSP_I2C4_ReadReg(unsigned short DevAddr, unsigned short Reg, unsigned char* pData, unsigned short Length) { return 0; }
    int BSP_I2C4_WriteReg(unsigned short DevAddr, unsigned short Reg, unsigned char* pData, unsigned short Length) { return 0; }
    unsigned int BSP_GetTick(void) { return 0; }
    
    // DMA2D存根函数
    void DMA2D_CropBuffer(void) {}
    void DMA2D_CopyBuffer(void) {}
    void DMA2D_CopyBufferEnd(void) {}
    void DMA2D_ExternalJobCompleted(void) {}
    
    // JPEG存根函数
    unsigned int JPEG_OUT_Read_BufferIndex = 0;
    void* Jpeg_OUT_BufferTab = 0;
    
    // FrontendApplication构造函数存根
    void _ZN19FrontendApplicationC1ER5ModelR12FrontendHeap(void) {}
    
    // VectorFontRenderer存根
    void _ZN8touchgfx22VectorFontRendererImpl20getVectorFontBuffersERPfRiRPhS4_(void) {}

    // VectorFontRendererImpl::getVectorFontBuffers 的C++实现
    namespace touchgfx {
        class VectorFontRendererImpl {
        public:
            static void getVectorFontBuffers(float*& vectorBuffer, int& vectorBufferSize, unsigned char*& glyphBuffer, int& glyphBufferSize) {
                // 存根实现 - 返回空缓冲区
                static float dummyVectorBuffer[1] = {0.0f};
                static unsigned char dummyGlyphBuffer[1] = {0};
                vectorBuffer = dummyVectorBuffer;
                vectorBufferSize = 1;
                glyphBuffer = dummyGlyphBuffer;
                glyphBufferSize = 1;
            }
        };
    }
    
    // HardwareMJPEGDecoder构造函数存根
    void _ZN20HardwareMJPEGDecoderC1Ev(void) {}
}

// 缺失的图像资源存根
extern "C" {
    const unsigned char image_vc1[] = {0};
    const unsigned char image_vc2[] = {0};
    const unsigned char image_vc3[] = {0};
    const unsigned char image_vc4[] = {0};
    const unsigned char image_vc5[] = {0};
    const unsigned char image_vc6[] = {0};
    const unsigned char image_vc7[] = {0};
    const unsigned char image_vc8[] = {0};
    
    // 字体kerning数据存根 - 空的kerning表
    const unsigned char kerning_verdana_10_4bpp[1] = {0};
    const unsigned char kerning_verdana_20_4bpp[1] = {0};
    const unsigned char kerning_verdana_40_4bpp[1] = {0};
}
