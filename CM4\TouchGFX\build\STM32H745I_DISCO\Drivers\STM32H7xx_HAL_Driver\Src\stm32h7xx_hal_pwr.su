stm32h7xx_hal_pwr.c:225:6:H<PERSON>_PWR_DeInit	0	static
stm32h7xx_hal_pwr.c:236:6:H<PERSON>_PWR_EnableBkUpAccess	0	static
stm32h7xx_hal_pwr.c:249:6:H<PERSON>_PWR_DisableBkUpAccess	0	static
stm32h7xx_hal_pwr.c:415:6:HAL_PWR_ConfigPVD	0	static
stm32h7xx_hal_pwr.c:470:6:HAL_PWR_EnablePVD	0	static
stm32h7xx_hal_pwr.c:480:6:H<PERSON>_PWR_DisablePVD	0	static
stm32h7xx_hal_pwr.c:507:6:HAL_PWR_EnableWakeUpPin	0	static
stm32h7xx_hal_pwr.c:536:6:HAL_PWR_DisableWakeUpPin	0	static
stm32h7xx_hal_pwr.c:564:6:H<PERSON>_PWR_EnterSLEEPMode	0	static
stm32h7xx_hal_pwr.c:618:6:HAL_PWR_EnterSTOPMode	16	static
stm32h7xx_hal_pwr.c:689:6:HAL_PWR_EnterSTANDBYMode	8	static
stm32h7xx_hal_pwr.c:741:6:HAL_PWR_EnableSleepOnExit	0	static
stm32h7xx_hal_pwr.c:754:6:HAL_PWR_DisableSleepOnExit	0	static
stm32h7xx_hal_pwr.c:767:6:HAL_PWR_EnableSEVOnPend	0	static
stm32h7xx_hal_pwr.c:779:6:HAL_PWR_DisableSEVOnPend	0	static
stm32h7xx_hal_pwr.c:853:13:HAL_PWR_PVDCallback	0	static
stm32h7xx_hal_pwr.c:808:6:HAL_PWR_PVD_IRQHandler	8	static
