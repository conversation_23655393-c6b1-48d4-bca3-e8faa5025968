stm32h7xx_hal_pwr_ex.c:313:19:H<PERSON>_PWREx_ConfigSupply	16	static
stm32h7xx_hal_pwr_ex.c:385:10:HAL_PWREx_GetSupplyConfig	0	static
stm32h7xx_hal_pwr_ex.c:413:19:HAL_PWREx_ControlVoltageScaling	16	static
stm32h7xx_hal_pwr_ex.c:512:10:HAL_PWREx_GetVoltageRange	0	static
stm32h7xx_hal_pwr_ex.c:538:19:HAL_PWREx_ControlStopModeVoltageScaling	0	static
stm32h7xx_hal_pwr_ex.c:553:10:HAL_PWREx_GetStopModeVoltageRange	0	static
stm32h7xx_hal_pwr_ex.c:816:6:HAL_PWREx_EnterSTOPMode	16	static
stm32h7xx_hal_pwr_ex.c:939:6:<PERSON><PERSON>_PWREx_ClearPendingEvent	8	static
stm32h7xx_hal_pwr_ex.c:986:6:HAL_PWREx_EnterSTANDBYMode	8	static
stm32h7xx_hal_pwr_ex.c:1081:6:HAL_PWREx_ConfigD3Domain	0	static
stm32h7xx_hal_pwr_ex.c:1101:6:HAL_PWREx_ClearDomainFlags	0	static
stm32h7xx_hal_pwr_ex.c:1135:19:HAL_PWREx_HoldCore	0	static
stm32h7xx_hal_pwr_ex.c:1184:6:HAL_PWREx_ReleaseCore	0	static
stm32h7xx_hal_pwr_ex.c:1212:6:HAL_PWREx_EnableFlashPowerDown	0	static
stm32h7xx_hal_pwr_ex.c:1226:6:HAL_PWREx_DisableFlashPowerDown	0	static
stm32h7xx_hal_pwr_ex.c:1302:6:HAL_PWREx_EnableWakeUpPin	12	static
stm32h7xx_hal_pwr_ex.c:1344:6:HAL_PWREx_DisableWakeUpPin	0	static
stm32h7xx_hal_pwr_ex.c:1369:10:HAL_PWREx_GetWakeupFlag	0	static
stm32h7xx_hal_pwr_ex.c:1394:19:HAL_PWREx_ClearWakeupFlag	0	static
stm32h7xx_hal_pwr_ex.c:1477:13:HAL_PWREx_WKUP1_Callback	0	static
stm32h7xx_hal_pwr_ex.c:1488:13:HAL_PWREx_WKUP2_Callback	0	static
stm32h7xx_hal_pwr_ex.c:1500:13:HAL_PWREx_WKUP3_Callback	0	static
stm32h7xx_hal_pwr_ex.c:1512:13:HAL_PWREx_WKUP4_Callback	0	static
stm32h7xx_hal_pwr_ex.c:1524:13:HAL_PWREx_WKUP5_Callback	0	static
stm32h7xx_hal_pwr_ex.c:1536:13:HAL_PWREx_WKUP6_Callback	0	static
stm32h7xx_hal_pwr_ex.c:1416:6:HAL_PWREx_WAKEUP_PIN_IRQHandler	8	static
stm32h7xx_hal_pwr_ex.c:1625:19:HAL_PWREx_EnableBkUpReg	16	static
stm32h7xx_hal_pwr_ex.c:1651:19:HAL_PWREx_DisableBkUpReg	16	static
stm32h7xx_hal_pwr_ex.c:1677:19:HAL_PWREx_EnableUSBReg	16	static
stm32h7xx_hal_pwr_ex.c:1703:19:HAL_PWREx_DisableUSBReg	16	static
stm32h7xx_hal_pwr_ex.c:1729:6:HAL_PWREx_EnableUSBVoltageDetector	0	static
stm32h7xx_hal_pwr_ex.c:1739:6:HAL_PWREx_DisableUSBVoltageDetector	0	static
stm32h7xx_hal_pwr_ex.c:1755:6:HAL_PWREx_EnableBatteryCharging	0	static
stm32h7xx_hal_pwr_ex.c:1771:6:HAL_PWREx_DisableBatteryCharging	0	static
stm32h7xx_hal_pwr_ex.c:1861:6:HAL_PWREx_EnableMonitoring	0	static
stm32h7xx_hal_pwr_ex.c:1871:6:HAL_PWREx_DisableMonitoring	0	static
stm32h7xx_hal_pwr_ex.c:1882:10:HAL_PWREx_GetTemperatureLevel	0	static
stm32h7xx_hal_pwr_ex.c:1913:10:HAL_PWREx_GetVBATLevel	0	static
stm32h7xx_hal_pwr_ex.c:1976:6:HAL_PWREx_ConfigAVD	0	static
stm32h7xx_hal_pwr_ex.c:2025:6:HAL_PWREx_EnableAVD	0	static
stm32h7xx_hal_pwr_ex.c:2035:6:HAL_PWREx_DisableAVD	0	static
stm32h7xx_hal_pwr_ex.c:2131:13:HAL_PWREx_AVDCallback	0	static
stm32h7xx_hal_pwr_ex.c:2046:6:HAL_PWREx_PVD_AVD_IRQHandler	16	static
