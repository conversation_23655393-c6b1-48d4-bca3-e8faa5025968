Archive member included to satisfy reference by file (symbol)

c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-exit.o)
                              c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard/crt0.o (exit)
c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-impure.o)
                              c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-exit.o) (_global_impure_ptr)
c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-init.o)
                              c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard/crt0.o (__libc_init_array)
c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-memset.o)
                              c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard/crt0.o (memset)
c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o)
                              c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-exit.o) (__call_exitprocs)
c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-atexit.o)
                              c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o) (atexit)
c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-fini.o)
                              c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o) (__libc_fini_array)
c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
                              c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o) (__retarget_lock_acquire_recursive)
c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__atexit.o)
                              c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-atexit.o) (__register_exitproc)
c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libnosys.a(_exit.o)
                              c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-exit.o) (_exit)

Allocating common symbols
Common symbol       size              file

__lock___atexit_recursive_mutex
                    0x1               c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
__lock___arc4random_mutex
                    0x1               c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
uwTick              0x4               CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
pFlash              0x1c              CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
__lock___env_recursive_mutex
                    0x1               c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
__lock___sinit_recursive_mutex
                    0x1               c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
__lock___malloc_recursive_mutex
                    0x1               c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
__lock___at_quick_exit_mutex
                    0x1               c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
__lock___dd_hash_mutex
                    0x1               c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
__lock___tz_mutex   0x1               c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
__lock___sfp_recursive_mutex
                    0x1               c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)

Discarded input sections

 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crti.o
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crti.o
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crti.o
 .data          0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtbegin.o
 .text          0x00000000       0x74 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard/crt0.o
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard/crt0.o
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard/crt0.o
 .ARM.extab     0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard/crt0.o
 .ARM.exidx     0x00000000        0x8 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard/crt0.o
 .ARM.attributes
                0x00000000       0x20 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard/crt0.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_MspInit
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_MspDeInit
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_DeInit
                0x00000000       0x84 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_GetTick
                0x00000000        0xc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_GetTickPrio
                0x00000000        0xc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SetTickFreq
                0x00000000       0x28 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_GetTickFreq
                0x00000000        0xc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_Delay
                0x00000000       0x28 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SuspendTick
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_ResumeTick
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_GetHalVersion
                0x00000000        0x8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_GetREVID
                0x00000000        0xc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_GetDEVID
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_GetUIDw0
                0x00000000        0xc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_GetUIDw1
                0x00000000        0xc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_GetUIDw2
                0x00000000        0xc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_VREFBUF_TrimmingConfig
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_EnableVREFBUF
                0x00000000       0x30 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_DisableVREFBUF
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_ETHInterfaceSelect
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_AnalogSwitchConfig
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_EnableBOOST
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_DisableBOOST
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_CM7BootAddConfig
                0x00000000       0x2c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_CM4BootAddConfig
                0x00000000       0x2c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_EnableCM7BOOT
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_DisableCM7BOOT
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_EnableCM4BOOT
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_DisableCM4BOOT
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_EnableCompensationCell
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_DisableCompensationCell
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_EnableIOSpeedOptimize
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_DisableIOSpeedOptimize
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_CompensationCodeSelect
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SYSCFG_CompensationCodeConfig
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_DBGMCU_EnableDBGSleepMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_DBGMCU_DisableDBGSleepMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_DBGMCU_EnableDBGStopMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_DBGMCU_DisableDBGStopMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_DBGMCU_EnableDBGStandbyMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_DBGMCU_DisableDBGStandbyMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_EnableDomain2DBGSleepMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_DisableDomain2DBGSleepMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_EnableDomain2DBGStopMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_DisableDomain2DBGStopMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_EnableDomain2DBGStandbyMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_DisableDomain2DBGStandbyMode
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_SetFMCMemorySwappingConfig
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_GetFMCMemorySwappingConfig
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_EXTI_EdgeConfig
                0x00000000       0x36 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_EXTI_GenerateSWInterrupt
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_EXTI_D1_ClearFlag
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_EXTI_D2_ClearFlag
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_EXTI_D1_EventInputConfig
                0x00000000       0x52 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_EXTI_D2_EventInputConfig
                0x00000000       0x52 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text.HAL_EXTI_D3_EventInputConfig
                0x00000000       0x44 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_NVIC_EnableIRQ
                0x00000000       0x1c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_NVIC_DisableIRQ
                0x00000000       0x28 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_NVIC_SystemReset
                0x00000000       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_MPU_Disable
                0x00000000       0x20 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_MPU_Enable
                0x00000000       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_MPU_EnableRegion
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_MPU_DisableRegion
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_MPU_ConfigRegion
                0x00000000       0x4c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_NVIC_GetPriorityGrouping
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_NVIC_GetPriority
                0x00000000       0x5c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_NVIC_SetPendingIRQ
                0x00000000       0x20 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_NVIC_GetPendingIRQ
                0x00000000       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_NVIC_ClearPendingIRQ
                0x00000000       0x20 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_NVIC_GetActive
                0x00000000       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_SYSTICK_CLKSourceConfig
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_SYSTICK_Callback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text.HAL_SYSTICK_IRQHandler
                0x00000000        0x8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.o
 .debug_info    0x00000000      0xa91 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.o
 .debug_abbrev  0x00000000      0x192 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.o
 .debug_aranges
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.o
 .debug_line    0x00000000      0x2f7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.o
 .debug_str     0x00000000      0x8b5 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.o
 .debug_info    0x00000000      0xa91 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.o
 .debug_abbrev  0x00000000      0x192 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.o
 .debug_aranges
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.o
 .debug_line    0x00000000      0x2f7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.o
 .debug_str     0x00000000      0x8b8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.DMA_SetConfig
                0x00000000      0x180 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.DMA_CalcBaseAndBitshift
                0x00000000       0x9c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.DMA_CalcDMAMUXChannelBaseAndMask
                0x00000000       0x90 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.DMA_CalcDMAMUXRequestGenBaseAndMask
                0x00000000       0x70 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_Init
                0x00000000      0x388 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_DeInit
                0x00000000      0x1b4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_Start
                0x00000000       0xec CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_Start_IT
                0x00000000      0x23c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_Abort
                0x00000000      0x360 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_Abort_IT
                0x00000000      0x168 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_PollForTransfer
                0x00000000      0x3d0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_IRQHandler
                0x00000000      0x548 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_RegisterCallback
                0x00000000       0x56 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_UnRegisterCallback
                0x00000000       0x62 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_GetState
                0x00000000        0x6 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text.HAL_DMA_GetError
                0x00000000        0x4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .rodata.flagBitshiftOffset.10051
                0x00000000        0x8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .debug_info    0x00000000     0x1745 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .debug_abbrev  0x00000000      0x349 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .debug_loc     0x00000000      0xa9a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .debug_aranges
                0x00000000       0x98 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .debug_ranges  0x00000000       0xc0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .debug_line    0x00000000     0x141a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .debug_str     0x00000000     0x1055 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .debug_frame   0x00000000      0x1c4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .text.DMA_MultiBufferSetConfig
                0x00000000       0x60 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .text.HAL_DMAEx_MultiBufferStart
                0x00000000      0x204 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .text.HAL_DMAEx_MultiBufferStart_IT
                0x00000000      0x348 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .text.HAL_DMAEx_ChangeMemory
                0x00000000       0x58 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .text.HAL_DMAEx_ConfigMuxSync
                0x00000000       0x60 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .text.HAL_DMAEx_ConfigMuxRequestGenerator
                0x00000000       0x4a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .text.HAL_DMAEx_EnableMuxRequestGenerator
                0x00000000       0x1a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .text.HAL_DMAEx_DisableMuxRequestGenerator
                0x00000000       0x1a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .text.HAL_DMAEx_MUX_IRQHandler
                0x00000000       0x56 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .debug_info    0x00000000     0x123f CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .debug_abbrev  0x00000000      0x2af CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .debug_loc     0x00000000      0x5d9 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .debug_aranges
                0x00000000       0x60 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .debug_ranges  0x00000000       0x50 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .debug_line    0x00000000      0x8a1 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .debug_str     0x00000000      0xe98 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .debug_frame   0x00000000      0x100 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.o
 .debug_info    0x00000000      0xa91 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.o
 .debug_abbrev  0x00000000      0x192 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.o
 .debug_aranges
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.o
 .debug_line    0x00000000      0x2f7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.o
 .debug_str     0x00000000      0x8b7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .text.HAL_EXTI_SetConfigLine
                0x00000000      0x118 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .text.HAL_EXTI_GetConfigLine
                0x00000000       0xec CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .text.HAL_EXTI_ClearConfigLine
                0x00000000       0xca CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .text.HAL_EXTI_RegisterCallback
                0x00000000        0xe CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .text.HAL_EXTI_GetHandle
                0x00000000        0xc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .text.HAL_EXTI_IRQHandler
                0x00000000       0x38 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .text.HAL_EXTI_GetPending
                0x00000000       0x2c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .text.HAL_EXTI_ClearPending
                0x00000000       0x26 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .text.HAL_EXTI_GenerateSWI
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .debug_info    0x00000000     0x13eb CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .debug_abbrev  0x00000000      0x2bc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .debug_loc     0x00000000      0x88d CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .debug_aranges
                0x00000000       0x60 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .debug_ranges  0x00000000       0x50 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .debug_line    0x00000000      0x522 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .debug_str     0x00000000      0xc5f CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .debug_frame   0x00000000      0x100 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.HAL_FLASH_EndOfOperationCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.HAL_FLASH_OperationErrorCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.HAL_FLASH_IRQHandler
                0x00000000      0x1ac CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.HAL_FLASH_Unlock
                0x00000000       0x4c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.HAL_FLASH_Lock
                0x00000000       0x30 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.HAL_FLASH_OB_Unlock
                0x00000000       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.HAL_FLASH_OB_Lock
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.HAL_FLASH_GetError
                0x00000000        0xc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.FLASH_WaitForLastOperation
                0x00000000       0xc4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.HAL_FLASH_Program
                0x00000000       0xb8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.HAL_FLASH_Program_IT
                0x00000000       0xb0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.FLASH_OB_WaitForLastOperation
                0x00000000       0x50 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.FLASH_CRC_WaitForLastOperation
                0x00000000       0x9c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text.HAL_FLASH_OB_Launch
                0x00000000       0x38 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .debug_info    0x00000000     0x1345 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .debug_abbrev  0x00000000      0x3a0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .debug_loc     0x00000000      0x678 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .debug_aranges
                0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .debug_ranges  0x00000000       0x70 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .debug_line    0x00000000      0x685 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .debug_str     0x00000000      0xc5d CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .debug_frame   0x00000000      0x158 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 COMMON         0x00000000       0x1c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text.FLASH_MassErase
                0x00000000       0x84 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text.HAL_FLASHEx_OBProgram
                0x00000000      0x2c8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text.HAL_FLASHEx_OBGetConfig
                0x00000000       0xdc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text.HAL_FLASHEx_Unlock_Bank1
                0x00000000       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text.HAL_FLASHEx_Lock_Bank1
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text.HAL_FLASHEx_Unlock_Bank2
                0x00000000       0x2c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text.HAL_FLASHEx_Lock_Bank2
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text.HAL_FLASHEx_ComputeCRC
                0x00000000      0x168 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text.FLASH_Erase_Sector
                0x00000000       0x48 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text.HAL_FLASHEx_Erase
                0x00000000      0x120 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text.HAL_FLASHEx_Erase_IT
                0x00000000       0xb8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .debug_info    0x00000000     0x1af3 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .debug_abbrev  0x00000000      0x3b2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .debug_loc     0x00000000      0x97d CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .debug_aranges
                0x00000000       0x70 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .debug_ranges  0x00000000      0x140 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .debug_line    0x00000000      0x6f3 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .debug_str     0x00000000      0xf78 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .debug_frame   0x00000000      0x110 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .text.HAL_GPIO_Init
                0x00000000      0x1dc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .text.HAL_GPIO_DeInit
                0x00000000      0x148 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .text.HAL_GPIO_ReadPin
                0x00000000        0xc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .text.HAL_GPIO_WritePin
                0x00000000        0xa CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .text.HAL_GPIO_TogglePin
                0x00000000       0x12 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .text.HAL_GPIO_LockPin
                0x00000000       0x2a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .text.HAL_GPIO_EXTI_Callback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .text.HAL_GPIO_EXTI_IRQHandler
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .debug_info    0x00000000     0x1708 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .debug_abbrev  0x00000000      0x2d5 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .debug_loc     0x00000000      0x2f2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .debug_aranges
                0x00000000       0x58 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .debug_ranges  0x00000000       0x60 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .debug_line    0x00000000      0x543 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .debug_str     0x00000000      0xdfb CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .debug_frame   0x00000000       0xd8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .text.HAL_HSEM_Take
                0x00000000       0x1e CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .text.HAL_HSEM_FastTake
                0x00000000       0x1c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .text.HAL_HSEM_IsSemTaken
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .text.HAL_HSEM_Release
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .text.HAL_HSEM_ReleaseAll
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .text.HAL_HSEM_SetClearKey
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .text.HAL_HSEM_GetClearKey
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .text.HAL_HSEM_DeactivateNotification
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .text.HAL_HSEM_FreeCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .text.HAL_HSEM_IRQHandler
                0x00000000       0x1c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_Flush_TXDR
                0x00000000       0x1c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_TransferConfig
                0x00000000       0x2c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_Enable_IRQ
                0x00000000       0x88 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_Disable_IRQ
                0x00000000       0x42 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_IsErrorOccurred
                0x00000000      0x100 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_WaitOnRXNEFlagUntilTimeout
                0x00000000       0xa4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_ConvertOtherXferOptions
                0x00000000       0x1a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_WaitOnFlagUntilTimeout
                0x00000000       0x76 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_WaitOnSTOPFlagUntilTimeout
                0x00000000       0x52 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_WaitOnTXISFlagUntilTimeout
                0x00000000       0x56 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_MspInit
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Init
                0x00000000       0xaa CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_MspDeInit
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_DeInit
                0x00000000       0x32 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Master_Transmit
                0x00000000      0x150 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Master_Receive
                0x00000000      0x124 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Slave_Transmit
                0x00000000      0x174 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Slave_Receive
                0x00000000      0x142 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Master_Transmit_IT
                0x00000000       0xac CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Master_Receive_IT
                0x00000000       0x88 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Slave_Transmit_IT
                0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Slave_Receive_IT
                0x00000000       0x58 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Master_Transmit_DMA
                0x00000000      0x160 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Master_Receive_DMA
                0x00000000      0x12c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Slave_Transmit_DMA
                0x00000000      0x118 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Slave_Receive_DMA
                0x00000000       0xd4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Mem_Write
                0x00000000      0x1b0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Mem_Read
                0x00000000      0x1bc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Mem_Write_IT
                0x00000000       0xa8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Mem_Read_IT
                0x00000000       0xa4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Mem_Write_DMA
                0x00000000      0x10c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Mem_Read_DMA
                0x00000000      0x110 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_IsDeviceReady
                0x00000000      0x102 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Master_Seq_Transmit_IT
                0x00000000       0xd0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Master_Seq_Transmit_DMA
                0x00000000      0x198 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Master_Seq_Receive_IT
                0x00000000       0xa0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Master_Seq_Receive_DMA
                0x00000000      0x14c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Slave_Seq_Transmit_IT
                0x00000000       0xc4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Slave_Seq_Transmit_DMA
                0x00000000      0x16c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Slave_Seq_Receive_IT
                0x00000000       0xc0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Slave_Seq_Receive_DMA
                0x00000000      0x168 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_EnableListen_IT
                0x00000000       0x28 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_DisableListen_IT
                0x00000000       0x38 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_Master_Abort_IT
                0x00000000       0x78 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_EV_IRQHandler
                0x00000000        0xe CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_MasterTxCpltCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_MasterRxCpltCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_ITMasterSeqCplt
                0x00000000       0x44 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_SlaveTxCpltCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_SlaveRxCpltCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_ITSlaveSeqCplt
                0x00000000       0x72 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_DMASlaveTransmitCplt
                0x00000000       0x1a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_DMASlaveReceiveCplt
                0x00000000       0x70 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_AddrCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_ITAddrCplt.isra.6
                0x00000000       0x8c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_ListenCpltCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_ITListenCplt
                0x00000000       0x64 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_MemTxCpltCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_MemRxCpltCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_ErrorCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_AbortCpltCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_TreatErrorCallback
                0x00000000       0x2c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_ITError
                0x00000000      0x108 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_ITSlaveCplt
                0x00000000      0x200 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_Slave_ISR_IT
                0x00000000      0x112 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_ITMasterCplt
                0x00000000       0xee CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_Master_ISR_IT
                0x00000000      0x12e CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_Mem_ISR_DMA
                0x00000000      0x124 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_Slave_ISR_DMA
                0x00000000      0x198 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_Master_ISR_DMA
                0x00000000      0x102 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_DMAError
                0x00000000       0xe4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_DMAMasterTransmitCplt
                0x00000000       0x50 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_DMAMasterReceiveCplt
                0x00000000       0x50 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_Mem_ISR_IT
                0x00000000      0x118 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_ER_IRQHandler
                0x00000000       0x60 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.I2C_DMAAbort
                0x00000000       0x16 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_GetState
                0x00000000        0x6 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_GetMode
                0x00000000        0x6 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text.HAL_I2C_GetError
                0x00000000        0x4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .debug_info    0x00000000     0x4443 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .debug_abbrev  0x00000000      0x3f3 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .debug_loc     0x00000000     0x3f79 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .debug_aranges
                0x00000000      0x240 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .debug_ranges  0x00000000      0x2c8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .debug_line    0x00000000     0x1f64 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .debug_str     0x00000000     0x1815 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .debug_frame   0x00000000      0x90c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .text.HAL_I2CEx_ConfigAnalogFilter
                0x00000000       0x4c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .text.HAL_I2CEx_ConfigDigitalFilter
                0x00000000       0x4a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .text.HAL_I2CEx_EnableWakeUp
                0x00000000       0x44 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .text.HAL_I2CEx_DisableWakeUp
                0x00000000       0x44 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .text.HAL_I2CEx_EnableFastModePlus
                0x00000000       0x30 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .text.HAL_I2CEx_DisableFastModePlus
                0x00000000       0x34 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .debug_info    0x00000000     0x171d CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .debug_abbrev  0x00000000      0x261 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .debug_loc     0x00000000      0x1a0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .debug_aranges
                0x00000000       0x48 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .debug_ranges  0x00000000       0x68 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .debug_line    0x00000000      0x4a7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .debug_str     0x00000000     0x11c3 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .debug_frame   0x00000000       0x90 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.o
 .debug_info    0x00000000      0xa91 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.o
 .debug_abbrev  0x00000000      0x192 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.o
 .debug_aranges
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.o
 .debug_line    0x00000000      0x2f7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.o
 .debug_str     0x00000000      0x8b6 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.o
 .debug_info    0x00000000      0xa91 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.o
 .debug_abbrev  0x00000000      0x192 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.o
 .debug_aranges
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.o
 .debug_line    0x00000000      0x2f7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.o
 .debug_str     0x00000000      0x8b9 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.MDMA_SetConfig
                0x00000000       0x54 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_Init
                0x00000000       0xe0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_DeInit
                0x00000000       0x3e CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_ConfigPostRequestMask
                0x00000000       0x58 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_RegisterCallback
                0x00000000       0x56 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_UnRegisterCallback
                0x00000000       0x62 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_LinkedList_CreateNode
                0x00000000       0xde CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_LinkedList_AddNode
                0x00000000       0xd2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_LinkedList_RemoveNode
                0x00000000       0xba CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_LinkedList_EnableCircularMode
                0x00000000       0x4a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_LinkedList_DisableCircularMode
                0x00000000       0x4c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_Start
                0x00000000       0x66 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_Start_IT
                0x00000000       0x96 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_Abort
                0x00000000       0x6e CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_Abort_IT
                0x00000000       0x26 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_PollForTransfer
                0x00000000       0xe0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_GenerateSWRequest
                0x00000000       0x32 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_IRQHandler
                0x00000000      0x170 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_GetState
                0x00000000        0x6 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text.HAL_MDMA_GetError
                0x00000000        0x4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .debug_info    0x00000000     0x16fa CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .debug_abbrev  0x00000000      0x35b CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .debug_loc     0x00000000      0xcf4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .debug_aranges
                0x00000000       0xb8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .debug_ranges  0x00000000       0xa8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .debug_line    0x00000000      0x85f CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .debug_str     0x00000000     0x10b5 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .debug_frame   0x00000000      0x200 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_DeInit
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_EnableBkUpAccess
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_DisableBkUpAccess
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_ConfigPVD
                0x00000000       0x48 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_EnablePVD
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_DisablePVD
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_EnableWakeUpPin
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_DisableWakeUpPin
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_EnterSLEEPMode
                0x00000000       0x1c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_EnterSTOPMode
                0x00000000       0x54 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_EnterSTANDBYMode
                0x00000000       0x48 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_EnableSleepOnExit
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_DisableSleepOnExit
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_EnableSEVOnPend
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_DisableSEVOnPend
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_PVDCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text.HAL_PWR_PVD_IRQHandler
                0x00000000       0x40 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .debug_info    0x00000000     0x1269 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .debug_abbrev  0x00000000      0x245 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .debug_loc     0x00000000       0x84 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .debug_aranges
                0x00000000       0xa0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .debug_ranges  0x00000000       0x90 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .debug_line    0x00000000      0x50e CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .debug_str     0x00000000      0xc98 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .debug_frame   0x00000000      0x13c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_ConfigSupply
                0x00000000       0x78 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_GetSupplyConfig
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_ControlVoltageScaling
                0x00000000       0xb4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_GetVoltageRange
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_ControlStopModeVoltageScaling
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_GetStopModeVoltageRange
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnterSTANDBYMode
                0x00000000       0x64 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_ConfigD3Domain
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_ClearDomainFlags
                0x00000000       0x28 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_HoldCore
                0x00000000       0x34 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_ReleaseCore
                0x00000000       0x20 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableFlashPowerDown
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableFlashPowerDown
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableWakeUpPin
                0x00000000       0x70 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableWakeUpPin
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_GetWakeupFlag
                0x00000000        0xc CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_ClearWakeupFlag
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_WKUP1_Callback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_WKUP2_Callback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_WKUP3_Callback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_WKUP4_Callback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_WKUP5_Callback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_WKUP6_Callback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_WAKEUP_PIN_IRQHandler
                0x00000000       0x7c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableBkUpReg
                0x00000000       0x30 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableBkUpReg
                0x00000000       0x30 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableUSBReg
                0x00000000       0x30 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableUSBReg
                0x00000000       0x30 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableUSBVoltageDetector
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableUSBVoltageDetector
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableBatteryCharging
                0x00000000       0x1c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableBatteryCharging
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableMonitoring
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableMonitoring
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_GetTemperatureLevel
                0x00000000       0x20 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_GetVBATLevel
                0x00000000       0x20 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_ConfigAVD
                0x00000000       0x48 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableAVD
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableAVD
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_AVDCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text.HAL_PWREx_PVD_AVD_IRQHandler
                0x00000000       0xa8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.o
 .debug_info    0x00000000      0xa91 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.o
 .debug_abbrev  0x00000000      0x192 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.o
 .debug_aranges
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.o
 .debug_line    0x00000000      0x2f7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.o
 .debug_str     0x00000000      0x8b6 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_DeInit
                0x00000000      0x1ac CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_OscConfig
                0x00000000      0x55c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_MCOConfig
                0x00000000       0x98 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_EnableCSS
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_DisableCSS
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_ClockConfig
                0x00000000      0x244 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_GetHCLKFreq
                0x00000000       0x40 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_GetPCLK1Freq
                0x00000000       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_GetPCLK2Freq
                0x00000000       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_GetOscConfig
                0x00000000      0x108 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_GetClockConfig
                0x00000000       0x54 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_CSSCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text.HAL_RCC_NMI_IRQHandler
                0x00000000       0x1c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.RCCEx_PLL2_Config
                0x00000000       0xf0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.RCCEx_PLL3_Config
                0x00000000       0xf0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_PeriphCLKConfig
                0x00000000      0xaf4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetPeriphCLKConfig
                0x00000000      0x1a0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetD1PCLK1Freq
                0x00000000       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetD3PCLK1Freq
                0x00000000       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetPLL2ClockFreq
                0x00000000      0x14c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetPLL3ClockFreq
                0x00000000      0x14c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetPLL1ClockFreq
                0x00000000      0x14c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetPeriphCLKFreq
                0x00000000      0x34c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetD1SysClockFreq
                0x00000000       0x44 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_EnableLSECSS
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_DisableLSECSS
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_EnableLSECSS_IT
                0x00000000       0x30 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_WakeUpStopCLKConfig
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_KerWakeUpStopCLKConfig
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_EnableBootCore
                0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_WWDGxSysResetConfig
                0x00000000        0x4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRSConfig
                0x00000000       0x6c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRSSoftwareSynchronizationGenerate
                0x00000000       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRSGetSynchronizationInfo
                0x00000000       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRSWaitSynchronization
                0x00000000       0x94 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRS_SyncOkCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRS_SyncWarnCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRS_ExpectedSyncCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRS_ErrorCallback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRS_IRQHandler
                0x00000000       0x7c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_LSECSS_Callback
                0x00000000        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text.HAL_RCCEx_LSECSS_IRQHandler
                0x00000000       0x1c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .debug_info    0x00000000     0x2277 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .debug_abbrev  0x00000000      0x386 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .debug_loc     0x00000000      0xf48 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .debug_aranges
                0x00000000       0xe0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .debug_ranges  0x00000000      0x100 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .debug_line    0x00000000      0xa19 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .debug_str     0x00000000     0x162c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .debug_frame   0x00000000      0x284 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.o
 .debug_info    0x00000000      0xa91 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.o
 .debug_abbrev  0x00000000      0x192 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.o
 .debug_aranges
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.o
 .debug_line    0x00000000      0x2f7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.o
 .debug_str     0x00000000      0x8b7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.o
 .debug_info    0x00000000      0xa91 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.o
 .debug_abbrev  0x00000000      0x192 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.o
 .debug_aranges
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.o
 .debug_line    0x00000000      0x2f7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.o
 .debug_str     0x00000000      0x8b5 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.o
 .debug_info    0x00000000      0xa91 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.o
 .debug_abbrev  0x00000000      0x192 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.o
 .debug_aranges
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.o
 .debug_line    0x00000000      0x2f7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.o
 .debug_str     0x00000000      0x8b8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.o
 .debug_info    0x00000000      0xa91 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.o
 .debug_abbrev  0x00000000      0x192 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.o
 .debug_aranges
                0x00000000       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.o
 .debug_line    0x00000000      0x2f7 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.o
 .debug_str     0x00000000      0x8b4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.o
 .comment       0x00000000       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.o
 .ARM.attributes
                0x00000000       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .text.PeriphCommonClock_Config
                0x00000000       0x3e CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .text.Error_Handler
                0x00000000        0x4 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
 .text          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
 .text.SystemCoreClockUpdate
                0x00000000      0x144 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
 .text          0x00000000       0x14 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o
 .data          0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o
 .bss           0x00000000        0x0 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-exit.o)
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-exit.o)
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-exit.o)
 .text.exit     0x00000000       0x20 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-exit.o)
 .debug_frame   0x00000000       0x28 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-exit.o)
 .ARM.attributes
                0x00000000       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-exit.o)
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-impure.o)
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-impure.o)
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-impure.o)
 .data._impure_ptr
                0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-impure.o)
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-init.o)
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-init.o)
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-init.o)
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-memset.o)
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-memset.o)
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-memset.o)
 .text.memset   0x00000000       0x94 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-memset.o)
 .debug_frame   0x00000000       0x38 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-memset.o)
 .ARM.attributes
                0x00000000       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-memset.o)
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o)
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o)
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o)
 .text.__call_exitprocs
                0x00000000       0xe8 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o)
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-atexit.o)
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-atexit.o)
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-atexit.o)
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-fini.o)
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-fini.o)
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-fini.o)
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .text.__retarget_lock_init
                0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .text.__retarget_lock_init_recursive
                0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .text.__retarget_lock_close
                0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .text.__retarget_lock_close_recursive
                0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .text.__retarget_lock_acquire
                0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire
                0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire_recursive
                0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .text.__retarget_lock_release
                0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__atexit.o)
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__atexit.o)
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__atexit.o)
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libnosys.a(_exit.o)
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libnosys.a(_exit.o)
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libnosys.a(_exit.o)
 .text._exit    0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libnosys.a(_exit.o)
 .debug_frame   0x00000000       0x20 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libnosys.a(_exit.o)
 .ARM.attributes
                0x00000000       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libnosys.a(_exit.o)
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtend.o
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtend.o
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtend.o
 .eh_frame      0x00000000        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtend.o
 .ARM.attributes
                0x00000000       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtend.o
 .text          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtn.o
 .data          0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtn.o
 .bss           0x00000000        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtn.o

Memory Configuration

Name             Origin             Length             Attributes
RAM_D1           0x24000000         0x00080000         xrw
FLASH            0x08000000         0x00100000         xr
DTCMRAM          0x20000000         0x00020000         xrw
RAM_D2           0x30000000         0x00048000         xrw
RAM_D3           0x38000000         0x00010000         xrw
ITCMRAM          0x00000000         0x00010000         xrw
QUADSPI          0x90000000         0x07e00000         r
SDRAM            0xd0000000         0x01000000         xrw
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crti.o
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtbegin.o
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard/crt0.o
START GROUP
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libstdc++.a
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libsupc++.a
END GROUP
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
LOAD CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o
START GROUP
END GROUP
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libstdc++.a
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libm.a
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a
START GROUP
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard\libgcc.a
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libg.a
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a
END GROUP
START GROUP
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard\libgcc.a
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libnosys.a
END GROUP
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtend.o
LOAD c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtn.o
                0x24080000                _estack = (ORIGIN (RAM_D1) + LENGTH (RAM_D1))
                0x00000200                _Min_Heap_Size = 0x200
                0x00000400                _Min_Stack_Size = 0x400

.isr_vector     0x08000000      0x298
                0x08000000                . = ALIGN (0x4)
 *(.isr_vector)
 .isr_vector    0x08000000      0x298 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o
                0x08000000                g_pfnVectors
                0x08000298                . = ALIGN (0x4)

.text           0x08000298      0x6b0
                0x08000298                . = ALIGN (0x4)
 *(.text)
 .text          0x08000298       0x40 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtbegin.o
 *(.text*)
 .text.HAL_InitTick
                0x080002d8       0x4c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
                0x080002d8                HAL_InitTick
 .text.HAL_Init
                0x08000324       0x94 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
                0x08000324                HAL_Init
 .text.HAL_IncTick
                0x080003b8       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
                0x080003b8                HAL_IncTick
 .text.HAL_NVIC_SetPriorityGrouping
                0x080003d0       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
                0x080003d0                HAL_NVIC_SetPriorityGrouping
 .text.HAL_NVIC_SetPriority
                0x080003f4       0x68 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
                0x080003f4                HAL_NVIC_SetPriority
 .text.HAL_SYSTICK_Config
                0x0800045c       0x2c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
                0x0800045c                HAL_SYSTICK_Config
 .text.HAL_GetCurrentCPUID
                0x08000488       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
                0x08000488                HAL_GetCurrentCPUID
 .text.HAL_HSEM_ActivateNotification
                0x080004a0       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
                0x080004a0                HAL_HSEM_ActivateNotification
 .text.HAL_PWREx_EnterSTOPMode
                0x080004b0       0x84 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
                0x080004b0                HAL_PWREx_EnterSTOPMode
 .text.HAL_PWREx_ClearPendingEvent
                0x08000534       0x12 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
                0x08000534                HAL_PWREx_ClearPendingEvent
 *fill*         0x08000546        0x2 
 .text.HAL_RCC_GetSysClockFreq
                0x08000548      0x138 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
                0x08000548                HAL_RCC_GetSysClockFreq
 .text.startup.main
                0x08000680       0x9c CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
                0x08000680                main
 .text.NMI_Handler
                0x0800071c        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
                0x0800071c                NMI_Handler
 .text.HardFault_Handler
                0x0800071e        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
                0x0800071e                HardFault_Handler
 .text.MemManage_Handler
                0x08000720        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
                0x08000720                MemManage_Handler
 .text.BusFault_Handler
                0x08000722        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
                0x08000722                BusFault_Handler
 .text.UsageFault_Handler
                0x08000724        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
                0x08000724                UsageFault_Handler
 .text.SVC_Handler
                0x08000726        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
                0x08000726                SVC_Handler
 .text.DebugMon_Handler
                0x08000728        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
                0x08000728                DebugMon_Handler
 .text.PendSV_Handler
                0x0800072a        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
                0x0800072a                PendSV_Handler
 .text.SysTick_Handler
                0x0800072c        0x4 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
                0x0800072c                SysTick_Handler
 .text.HAL_MspInit
                0x08000730       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
                0x08000730                HAL_MspInit
 .text.SystemInit
                0x08000754       0x24 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
                0x08000754                SystemInit
 .text.Reset_Handler
                0x08000778       0x50 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o
                0x08000778                Reset_Handler
 .text.Default_Handler
                0x080007c8        0x2 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o
                0x080007c8                RTC_Alarm_IRQHandler
                0x080007c8                EXTI2_IRQHandler
                0x080007c8                TIM8_CC_IRQHandler
                0x080007c8                HRTIM1_Master_IRQHandler
                0x080007c8                UART8_IRQHandler
                0x080007c8                SPI4_IRQHandler
                0x080007c8                TIM1_CC_IRQHandler
                0x080007c8                BDMA_Channel6_IRQHandler
                0x080007c8                DMA2_Stream5_IRQHandler
                0x080007c8                HRTIM1_FLT_IRQHandler
                0x080007c8                JPEG_IRQHandler
                0x080007c8                DMA1_Stream5_IRQHandler
                0x080007c8                BDMA_Channel1_IRQHandler
                0x080007c8                HRTIM1_TIMD_IRQHandler
                0x080007c8                TAMP_STAMP_IRQHandler
                0x080007c8                EXTI3_IRQHandler
                0x080007c8                LPTIM4_IRQHandler
                0x080007c8                TIM8_TRG_COM_TIM14_IRQHandler
                0x080007c8                LPTIM2_IRQHandler
                0x080007c8                DFSDM1_FLT1_IRQHandler
                0x080007c8                DMAMUX2_OVR_IRQHandler
                0x080007c8                TIM8_UP_TIM13_IRQHandler
                0x080007c8                I2C3_ER_IRQHandler
                0x080007c8                DFSDM1_FLT2_IRQHandler
                0x080007c8                MDMA_IRQHandler
                0x080007c8                LPTIM3_IRQHandler
                0x080007c8                HSEM1_IRQHandler
                0x080007c8                EXTI0_IRQHandler
                0x080007c8                I2C2_EV_IRQHandler
                0x080007c8                DMA1_Stream2_IRQHandler
                0x080007c8                FPU_IRQHandler
                0x080007c8                OTG_HS_WKUP_IRQHandler
                0x080007c8                FDCAN1_IT1_IRQHandler
                0x080007c8                LTDC_ER_IRQHandler
                0x080007c8                DMA2_Stream2_IRQHandler
                0x080007c8                HRTIM1_TIME_IRQHandler
                0x080007c8                SPI1_IRQHandler
                0x080007c8                TIM6_DAC_IRQHandler
                0x080007c8                DCMI_IRQHandler
                0x080007c8                HRTIM1_TIMC_IRQHandler
                0x080007c8                DMA2_Stream3_IRQHandler
                0x080007c8                SAI2_IRQHandler
                0x080007c8                DFSDM1_FLT3_IRQHandler
                0x080007c8                USART6_IRQHandler
                0x080007c8                TIM17_IRQHandler
                0x080007c8                USART3_IRQHandler
                0x080007c8                BDMA_Channel7_IRQHandler
                0x080007c8                LPTIM5_IRQHandler
                0x080007c8                UART5_IRQHandler
                0x080007c8                ADC3_IRQHandler
                0x080007c8                DMA2_Stream0_IRQHandler
                0x080007c8                TIM4_IRQHandler
                0x080007c8                BDMA_Channel2_IRQHandler
                0x080007c8                QUADSPI_IRQHandler
                0x080007c8                I2C1_EV_IRQHandler
                0x080007c8                DMA1_Stream6_IRQHandler
                0x080007c8                DMAMUX1_OVR_IRQHandler
                0x080007c8                DMA1_Stream1_IRQHandler
                0x080007c8                TIM16_IRQHandler
                0x080007c8                UART4_IRQHandler
                0x080007c8                BDMA_Channel5_IRQHandler
                0x080007c8                TIM3_IRQHandler
                0x080007c8                RCC_IRQHandler
                0x080007c8                TIM8_BRK_TIM12_IRQHandler
                0x080007c8                TIM1_TRG_COM_IRQHandler
                0x080007c8                Default_Handler
                0x080007c8                ECC_IRQHandler
                0x080007c8                CEC_IRQHandler
                0x080007c8                EXTI15_10_IRQHandler
                0x080007c8                BDMA_Channel0_IRQHandler
                0x080007c8                ADC_IRQHandler
                0x080007c8                DMA1_Stream7_IRQHandler
                0x080007c8                HRTIM1_TIMA_IRQHandler
                0x080007c8                SPI5_IRQHandler
                0x080007c8                TIM7_IRQHandler
                0x080007c8                SDMMC1_IRQHandler
                0x080007c8                TIM5_IRQHandler
                0x080007c8                DMA2_Stream7_IRQHandler
                0x080007c8                TIM15_IRQHandler
                0x080007c8                PVD_AVD_IRQHandler
                0x080007c8                I2C3_EV_IRQHandler
                0x080007c8                EXTI9_5_IRQHandler
                0x080007c8                RTC_WKUP_IRQHandler
                0x080007c8                LTDC_IRQHandler
                0x080007c8                SAI3_IRQHandler
                0x080007c8                ETH_WKUP_IRQHandler
                0x080007c8                SPDIF_RX_IRQHandler
                0x080007c8                SPI2_IRQHandler
                0x080007c8                OTG_HS_EP1_IN_IRQHandler
                0x080007c8                DMA1_Stream0_IRQHandler
                0x080007c8                CRS_IRQHandler
                0x080007c8                CM7_SEV_IRQHandler
                0x080007c8                EXTI4_IRQHandler
                0x080007c8                RNG_IRQHandler
                0x080007c8                CM4_SEV_IRQHandler
                0x080007c8                HRTIM1_TIMB_IRQHandler
                0x080007c8                FDCAN2_IT1_IRQHandler
                0x080007c8                ETH_IRQHandler
                0x080007c8                TIM1_UP_IRQHandler
                0x080007c8                WWDG_RST_IRQHandler
                0x080007c8                BDMA_Channel4_IRQHandler
                0x080007c8                OTG_HS_EP1_OUT_IRQHandler
                0x080007c8                WWDG_IRQHandler
                0x080007c8                SPI6_IRQHandler
                0x080007c8                MDIOS_IRQHandler
                0x080007c8                I2C4_EV_IRQHandler
                0x080007c8                FDCAN2_IT0_IRQHandler
                0x080007c8                LPUART1_IRQHandler
                0x080007c8                FDCAN1_IT0_IRQHandler
                0x080007c8                TIM2_IRQHandler
                0x080007c8                OTG_FS_WKUP_IRQHandler
                0x080007c8                OTG_HS_IRQHandler
                0x080007c8                HSEM2_IRQHandler
                0x080007c8                DMA2D_IRQHandler
                0x080007c8                TIM1_BRK_IRQHandler
                0x080007c8                EXTI1_IRQHandler
                0x080007c8                SDMMC2_IRQHandler
                0x080007c8                OTG_FS_EP1_OUT_IRQHandler
                0x080007c8                BDMA_Channel3_IRQHandler
                0x080007c8                HOLD_CORE_IRQHandler
                0x080007c8                UART7_IRQHandler
                0x080007c8                MDIOS_WKUP_IRQHandler
                0x080007c8                USART2_IRQHandler
                0x080007c8                DFSDM1_FLT0_IRQHandler
                0x080007c8                SAI4_IRQHandler
                0x080007c8                I2C2_ER_IRQHandler
                0x080007c8                DMA2_Stream1_IRQHandler
                0x080007c8                FLASH_IRQHandler
                0x080007c8                DMA2_Stream4_IRQHandler
                0x080007c8                USART1_IRQHandler
                0x080007c8                OTG_FS_IRQHandler
                0x080007c8                SPI3_IRQHandler
                0x080007c8                WAKEUP_PIN_IRQHandler
                0x080007c8                DMA1_Stream4_IRQHandler
                0x080007c8                OTG_FS_EP1_IN_IRQHandler
                0x080007c8                I2C1_ER_IRQHandler
                0x080007c8                FMC_IRQHandler
                0x080007c8                FDCAN_CAL_IRQHandler
                0x080007c8                SWPMI1_IRQHandler
                0x080007c8                COMP1_IRQHandler
                0x080007c8                LPTIM1_IRQHandler
                0x080007c8                I2C4_ER_IRQHandler
                0x080007c8                DMA2_Stream6_IRQHandler
                0x080007c8                SAI1_IRQHandler
                0x080007c8                DMA1_Stream3_IRQHandler
 *fill*         0x080007ca        0x2 
 .text.__libc_init_array
                0x080007cc       0x48 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-init.o)
                0x080007cc                __libc_init_array
 .text.startup.register_fini
                0x08000814       0x14 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o)
 .text.atexit   0x08000828        0xc c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-atexit.o)
                0x08000828                atexit
 .text.__libc_fini_array
                0x08000834       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-fini.o)
                0x08000834                __libc_fini_array
 .text.__retarget_lock_acquire_recursive
                0x08000868        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
                0x08000868                __retarget_lock_acquire_recursive
 .text.__retarget_lock_release_recursive
                0x0800086c        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
                0x0800086c                __retarget_lock_release_recursive
 .text.__register_exitproc
                0x08000870       0xc0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__atexit.o)
                0x08000870                __register_exitproc
 *(.glue_7)
 .glue_7        0x08000930        0x0 linker stubs
 *(.glue_7t)
 .glue_7t       0x08000930        0x0 linker stubs
 *(.eh_frame)
 .eh_frame      0x08000930        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtbegin.o
 *(.init)
 .init          0x08000930        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crti.o
                0x08000930                _init
 .init          0x08000934        0x8 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtn.o
 *(.fini)
 .fini          0x0800093c        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crti.o
                0x0800093c                _fini
 .fini          0x08000940        0x8 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtn.o
                0x08000948                . = ALIGN (0x4)
                0x08000948                _etext = .

.vfp11_veneer   0x08000948        0x0
 .vfp11_veneer  0x08000948        0x0 linker stubs

.v4_bx          0x08000948        0x0
 .v4_bx         0x08000948        0x0 linker stubs

.iplt           0x08000948        0x0
 .iplt          0x08000948        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtbegin.o

.rodata         0x08000948       0x14
                0x08000948                . = ALIGN (0x4)
 *(.rodata)
 *(.rodata*)
 .rodata.D1CorePrescTable
                0x08000948       0x10 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
                0x08000948                D1CorePrescTable
 .rodata._global_impure_ptr
                0x08000958        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-impure.o)
                0x08000958                _global_impure_ptr
                0x0800095c                . = ALIGN (0x4)

.rel.dyn        0x0800095c        0x0
 .rel.iplt      0x0800095c        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtbegin.o

.ARM.extab      0x0800095c        0x0
                0x0800095c                . = ALIGN (0x4)
 *(.ARM.extab* .gnu.linkonce.armextab.*)
                0x0800095c                . = ALIGN (0x4)

.ARM            0x0800095c        0x0
                0x0800095c                . = ALIGN (0x4)
                0x0800095c                __exidx_start = .
 *(.ARM.exidx*)
                0x0800095c                __exidx_end = .
                0x0800095c                . = ALIGN (0x4)

.preinit_array  0x0800095c        0x0
                0x0800095c                . = ALIGN (0x4)
                0x0800095c                PROVIDE (__preinit_array_start = .)
 *(.preinit_array*)
                0x0800095c                PROVIDE (__preinit_array_end = .)
                0x0800095c                . = ALIGN (0x4)

.init_array     0x0800095c        0x8
                0x0800095c                . = ALIGN (0x4)
                0x0800095c                PROVIDE (__init_array_start = .)
 *(SORT_BY_NAME(.init_array.*))
 .init_array.00000
                0x0800095c        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o)
 *(.init_array*)
 .init_array    0x08000960        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtbegin.o
                0x08000964                PROVIDE (__init_array_end = .)
                0x08000964                . = ALIGN (0x4)

.fini_array     0x08000964        0x4
                0x08000964                . = ALIGN (0x4)
                0x08000964                PROVIDE (__fini_array_start = .)
 *(SORT_BY_NAME(.fini_array.*))
 *(.fini_array*)
 .fini_array    0x08000964        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtbegin.o
                0x08000968                PROVIDE (__fini_array_end = .)
                0x08000968                . = ALIGN (0x4)
                0x08000968                _sidata = LOADADDR (.data)

.data           0x24000000      0x43c load address 0x08000968
                0x24000000                . = ALIGN (0x4)
                0x24000000                _sdata = .
 *(.data)
 *(.data*)
 .data.uwTickFreq
                0x24000000        0x1 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
                0x24000000                uwTickFreq
 *fill*         0x24000001        0x3 
 .data.uwTickPrio
                0x24000004        0x4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
                0x24000004                uwTickPrio
 .data.SystemCoreClock
                0x24000008        0x4 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
                0x24000008                SystemCoreClock
 .data.SystemD2Clock
                0x2400000c        0x4 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
                0x2400000c                SystemD2Clock
 .data.impure_data
                0x24000010      0x428 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-impure.o)
 .data.__atexit_recursive_mutex
                0x24000438        0x4 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o)
                0x24000438                __atexit_recursive_mutex
 *(.RamFunc)
 *(.RamFunc*)
                0x2400043c                . = ALIGN (0x4)
                0x2400043c                _edata = .

.igot.plt       0x2400043c        0x0 load address 0x08000da4
 .igot.plt      0x2400043c        0x0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtbegin.o
                0x2400043c                . = ALIGN (0x4)

.bss            0x2400043c       0x44 load address 0x08000da4
                0x2400043c                _sbss = .
                0x2400043c                __bss_start__ = _sbss
 *(.bss)
 .bss           0x2400043c       0x1c c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtbegin.o
 *(.bss*)
 *(COMMON)
 COMMON         0x24000458        0x4 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
                0x24000458                uwTick
 COMMON         0x2400045c       0x21 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
                0x2400045c                __lock___atexit_recursive_mutex
                0x24000460                __lock___arc4random_mutex
                0x24000464                __lock___env_recursive_mutex
                0x24000468                __lock___sinit_recursive_mutex
                0x2400046c                __lock___malloc_recursive_mutex
                0x24000470                __lock___at_quick_exit_mutex
                0x24000474                __lock___dd_hash_mutex
                0x24000478                __lock___tz_mutex
                0x2400047c                __lock___sfp_recursive_mutex
                0x24000480                . = ALIGN (0x4)
 *fill*         0x2400047d        0x3 
                0x24000480                _ebss = .
                0x24000480                __bss_end__ = _ebss

._user_heap_stack
                0x24000480      0x600 load address 0x08000da4
                0x24000480                . = ALIGN (0x8)
                [!provide]                PROVIDE (end = .)
                [!provide]                PROVIDE (_end = .)
                0x24000680                . = (. + _Min_Heap_Size)
 *fill*         0x24000480      0x200 
                0x24000a80                . = (. + _Min_Stack_Size)
 *fill*         0x24000680      0x400 
                0x24000a80                . = ALIGN (0x8)

/DISCARD/
 libc.a(*)
 libm.a(*)
 libgcc.a(*)

.ARM.attributes
                0x00000000       0x30
 *(.ARM.attributes)
 .ARM.attributes
                0x00000000       0x22 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crti.o
 .ARM.attributes
                0x00000022       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtbegin.o
 .ARM.attributes
                0x00000056       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .ARM.attributes
                0x0000008f       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .ARM.attributes
                0x000000c8       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .ARM.attributes
                0x00000101       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .ARM.attributes
                0x0000013a       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .ARM.attributes
                0x00000173       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .ARM.attributes
                0x000001ac       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
 .ARM.attributes
                0x000001e5       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
 .ARM.attributes
                0x0000021e       0x39 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
 .ARM.attributes
                0x00000257       0x21 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o
 .ARM.attributes
                0x00000278       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-impure.o)
 .ARM.attributes
                0x000002ac       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-init.o)
 .ARM.attributes
                0x000002e0       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o)
 .ARM.attributes
                0x00000314       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-atexit.o)
 .ARM.attributes
                0x00000348       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-fini.o)
 .ARM.attributes
                0x0000037c       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .ARM.attributes
                0x000003b0       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__atexit.o)
 .ARM.attributes
                0x000003e4       0x22 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/thumb/v7e-m/fpv4-sp/hard/crtn.o

FontFlashSection
                0x90000000        0x0
 *(FontFlashSection FontFlashSection.*)
 *(.gnu.linkonce.r.*)
                0x90000000                . = ALIGN (0x4)

TextFlashSection
                0x90000000        0x0
 *(TextFlashSection TextFlashSection.*)
 *(.gnu.linkonce.r.*)
                0x90000000                . = ALIGN (0x4)

ExtFlashSection
                0x90000000        0x0
 *(ExtFlashSection ExtFlashSection.*)
 *(.gnu.linkonce.r.*)
                0x90000000                . = ALIGN (0x4)

BufferSection   0xd0000000        0x0
 *(TouchGFX_Framebuffer TouchGFX_Framebuffer.*)
 *(.gnu.linkonce.r.*)
                0xd0000000                . = ALIGN (0x4)
 *(Video_RGB_Buffer Video_RGB_Buffer.*)
 *(.gnu.linkonce.r.*)
                0xd0000000                . = ALIGN (0x4)
OUTPUT(CM4/TouchGFX/build/bin/target.elf elf32-littlearm)

.debug_info     0x00000000     0xbebb
 .debug_info    0x00000000     0x2125 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .debug_info    0x00002125     0x1a72 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .debug_info    0x00003b97      0xdce CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .debug_info    0x00004965     0x1adf CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .debug_info    0x00006444     0x1aaa CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .debug_info    0x00007eee     0x1573 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .debug_info    0x00009461      0xb3e CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
 .debug_info    0x00009f9f      0xe4b CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
 .debug_info    0x0000adea     0x10af CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
 .debug_info    0x0000be99       0x22 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o

.debug_abbrev   0x00000000     0x1a12
 .debug_abbrev  0x00000000      0x37a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .debug_abbrev  0x0000037a      0x3de CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .debug_abbrev  0x00000758      0x2c6 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .debug_abbrev  0x00000a1e      0x376 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .debug_abbrev  0x00000d94      0x343 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .debug_abbrev  0x000010d7      0x309 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .debug_abbrev  0x000013e0      0x217 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
 .debug_abbrev  0x000015f7      0x1e0 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
 .debug_abbrev  0x000017d7      0x229 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
 .debug_abbrev  0x00001a00       0x12 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o

.debug_loc      0x00000000     0x15f9
 .debug_loc     0x00000000      0x4b8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .debug_loc     0x000004b8      0x487 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .debug_loc     0x0000093f      0x149 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .debug_loc     0x00000a88      0x43f CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .debug_loc     0x00000ec7      0x59c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .debug_loc     0x00001463      0x196 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o

.debug_aranges  0x00000000      0x5e0
 .debug_aranges
                0x00000000      0x1f8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .debug_aranges
                0x000001f8       0xc0 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .debug_aranges
                0x000002b8       0x70 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .debug_aranges
                0x00000328      0x140 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .debug_aranges
                0x00000468       0x88 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .debug_aranges
                0x000004f0       0x30 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .debug_aranges
                0x00000520       0x50 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
 .debug_aranges
                0x00000570       0x20 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
 .debug_aranges
                0x00000590       0x28 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
 .debug_aranges
                0x000005b8       0x28 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o

.debug_ranges   0x00000000      0x6a0
 .debug_ranges  0x00000000      0x200 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .debug_ranges  0x00000200      0x150 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .debug_ranges  0x00000350       0x60 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .debug_ranges  0x000003b0      0x130 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .debug_ranges  0x000004e0       0xe8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .debug_ranges  0x000005c8       0x38 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .debug_ranges  0x00000600       0x40 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
 .debug_ranges  0x00000640       0x28 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
 .debug_ranges  0x00000668       0x18 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
 .debug_ranges  0x00000680       0x20 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o

.debug_line     0x00000000     0x30b3
 .debug_line    0x00000000      0x82c CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .debug_line    0x0000082c      0x616 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .debug_line    0x00000e42      0x41a CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .debug_line    0x0000125c      0x799 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .debug_line    0x000019f5      0x7e5 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .debug_line    0x000021da      0x3e1 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .debug_line    0x000025bb      0x388 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
 .debug_line    0x00002943      0x341 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
 .debug_line    0x00002c84      0x3af CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
 .debug_line    0x00003033       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o

.debug_str      0x00000000     0x32ba
 .debug_str     0x00000000     0x1bee CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
                               0x1cf8 (size before relaxing)
 .debug_str     0x00001bee      0x4f6 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
                               0x16b1 (size before relaxing)
 .debug_str     0x000020e4      0x1a6 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
                                0xa68 (size before relaxing)
 .debug_str     0x0000228a      0x5de CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
                               0x1123 (size before relaxing)
 .debug_str     0x00002868      0x525 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
                               0x10c2 (size before relaxing)
 .debug_str     0x00002d8d      0x3dd CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
                                0xfc8 (size before relaxing)
 .debug_str     0x0000316a       0xac CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
                                0x938 (size before relaxing)
 .debug_str     0x00003216       0x21 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
                                0xad7 (size before relaxing)
 .debug_str     0x00003237       0x55 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
                                0xba7 (size before relaxing)
 .debug_str     0x0000328c       0x2e CM4/TouchGFX/build/STM32H745I_DISCO/gcc/startup_stm32h745xihx_cm4.o
                                 0x52 (size before relaxing)

.comment        0x00000000       0x7f
 .comment       0x00000000       0x7f CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
                                 0x80 (size before relaxing)
 .comment       0x0000007f       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .comment       0x0000007f       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .comment       0x0000007f       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .comment       0x0000007f       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .comment       0x0000007f       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .comment       0x0000007f       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
 .comment       0x0000007f       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
 .comment       0x0000007f       0x80 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o

.debug_frame    0x00000000      0xe64
 .debug_frame   0x00000000      0x454 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.o
 .debug_frame   0x00000454      0x180 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.o
 .debug_frame   0x000005d4       0xc8 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.o
 .debug_frame   0x0000069c      0x344 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.o
 .debug_frame   0x000009e0      0x170 CM4/TouchGFX/build/STM32H745I_DISCO/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.o
 .debug_frame   0x00000b50       0x54 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/main.o
 .debug_frame   0x00000ba4       0xa0 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_it.o
 .debug_frame   0x00000c44       0x28 CM4/TouchGFX/build/STM32H745I_DISCO/CM4/Core/Src/stm32h7xx_hal_msp.o
 .debug_frame   0x00000c6c       0x38 CM4/TouchGFX/build/STM32H745I_DISCO/Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.o
 .debug_frame   0x00000ca4       0x2c c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-init.o)
 .debug_frame   0x00000cd0       0x5c c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__call_atexit.o)
 .debug_frame   0x00000d2c       0x20 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-atexit.o)
 .debug_frame   0x00000d4c       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-fini.o)
 .debug_frame   0x00000d80       0xb0 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-lock.o)
 .debug_frame   0x00000e30       0x34 c:/users/<USER>/stmicroelectronics/touchgfx/4.25.0/env/mingw/msys/1.0/gnu-arm-gcc/bin/../lib/gcc/arm-none-eabi/7.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m/fpv4-sp/hard\libc.a(lib_a-__atexit.o)
