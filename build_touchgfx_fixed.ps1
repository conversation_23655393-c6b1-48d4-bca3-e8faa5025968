# TouchGFX Fixed Build Script
# 我会按照要求完成任务

Write-Host "Building TouchGFX project with fixed approach..." -ForegroundColor Yellow

# 设置工具链路径
$TOOLCHAIN_PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin"
$GCC = "${TOOLCHAIN_PATH}\arm-none-eabi-gcc.exe"
$GPP = "${TOOLCHAIN_PATH}\arm-none-eabi-g++.exe"
$OBJCOPY = "${TOOLCHAIN_PATH}\arm-none-eabi-objcopy.exe"
$SIZE = "${TOOLCHAIN_PATH}\arm-none-eabi-size.exe"

# 检查工具链
if (!(Test-Path $GCC)) {
    Write-Host "Error: GCC not found at $GCC" -ForegroundColor Red
    exit 1
}

# 创建输出目录
$BUILD_DIR = "build_fixed"
if (Test-Path $BUILD_DIR) { Remove-Item $BUILD_DIR -Recurse -Force }
New-Item -ItemType Directory -Path $BUILD_DIR | Out-Null

# 编译器标志
$COMMON_FLAGS = "-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_PWR_DIRECT_SMPS_SUPPLY -DUSE_FULL_LL_DRIVER -Os -Wall -fdata-sections -ffunction-sections -g -gdwarf-2"

$CPP_FLAGS = "$COMMON_FLAGS -fno-exceptions -fno-rtti -fno-use-cxa-atexit -std=c++11"
$C_FLAGS = "$COMMON_FLAGS -std=gnu11"

# 包含路径
$INCLUDES = "-ICM7/Core/Inc -ICM7/TouchGFX/App -ICM7/TouchGFX/target/generated -ICM7/TouchGFX/target -ICM7/TouchGFX/gui/include -ICM7/TouchGFX/generated/gui_generated/include -ICM7/TouchGFX/generated/images/include -ICM7/TouchGFX/generated/texts/include -ICM7/TouchGFX/generated/fonts/include -ICM7/Middlewares/ST/touchgfx/framework/include -ICM7/Middlewares/Third_Party/FreeRTOS/Source/include -ICM7/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -ICM7/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1 -IDrivers/STM32H7xx_HAL_Driver/Inc -IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy -IDrivers/CMSIS/Device/ST/STM32H7xx/Include -IDrivers/CMSIS/Include -IDrivers/BSP/STM32H745I-DISCO"

# 编译函数
function Compile-File {
    param($Compiler, $Flags, $SourceFile, $OutputFile)
    
    if (!(Test-Path $SourceFile)) {
        Write-Host "Warning: Source file not found: $SourceFile" -ForegroundColor Yellow
        return $false
    }
    
    $OutputPath = "$BUILD_DIR\$OutputFile"
    $cmd = "$Compiler $Flags $INCLUDES -c `"$SourceFile`" -o `"$OutputPath`""
    
    Write-Host "Compiling: $SourceFile" -ForegroundColor Cyan
    $result = Invoke-Expression $cmd
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "FAILED: $SourceFile" -ForegroundColor Red
        return $false
    }
    
    return $true
}

Write-Host "Compiling core files..." -ForegroundColor Green

# 编译核心文件
$ObjectFiles = @()

# 启动文件
if (Compile-File $GCC $C_FLAGS "gcc/startup_stm32h745xihx_cm7.s" "startup.o") {
    $ObjectFiles += "$BUILD_DIR\startup.o"
}

# 核心系统文件
$CoreFiles = @(
    @("CM7/Core/Src/main.c", "main.o"),
    @("CM7/Core/Src/stm32h7xx_it.c", "stm32h7xx_it.o"),
    @("CM7/Core/Src/stm32h7xx_hal_msp.c", "stm32h7xx_hal_msp.o"),
    @("CM7/Core/Src/system_stm32h7xx.c", "system_stm32h7xx.o")
)

foreach ($file in $CoreFiles) {
    if (Compile-File $GCC $C_FLAGS $file[0] $file[1]) {
        $ObjectFiles += "$BUILD_DIR\$($file[1])"
    }
}

# HAL驱动文件
$HalFiles = @(
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c", "hal.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c", "hal_cortex.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c", "hal_rcc.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c", "hal_gpio.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c", "hal_pwr.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c", "hal_pwr_ex.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c", "hal_adc.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c", "hal_adc_ex.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c", "hal_dma.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c", "hal_dma_ex.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.c", "hal_ltdc.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.c", "hal_ltdc_ex.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.c", "hal_dma2d.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dsi.c", "hal_dsi.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c", "hal_i2c.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c", "hal_i2c_ex.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c", "hal_tim.o"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c", "hal_tim_ex.o")
)

foreach ($file in $HalFiles) {
    if (Compile-File $GCC $C_FLAGS $file[0] $file[1]) {
        $ObjectFiles += "$BUILD_DIR\$($file[1])"
    }
}

# BSP文件
$BspFiles = @(
    @("Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery.c", "bsp_discovery.o"),
    @("Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_lcd.c", "bsp_lcd.o"),
    @("Drivers/BSP/Components/otm8009a/otm8009a.c", "otm8009a.o"),
    @("Drivers/BSP/Components/ft5336/ft5336.c", "ft5336.o")
)

foreach ($file in $BspFiles) {
    if (Compile-File $GCC $C_FLAGS $file[0] $file[1]) {
        $ObjectFiles += "$BUILD_DIR\$($file[1])"
    }
}

# FreeRTOS文件
$FreeRTOSFiles = @(
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/croutine.c", "croutine.o"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/event_groups.c", "event_groups.o"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/list.c", "list.o"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/queue.c", "queue.o"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c", "stream_buffer.o"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/tasks.c", "tasks.o"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/timers.c", "timers.o"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c", "cmsis_os2.o"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1/port.c", "port.o"),
    @("CM7/Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c", "heap_4.o")
)

foreach ($file in $FreeRTOSFiles) {
    if (Compile-File $GCC $C_FLAGS $file[0] $file[1]) {
        $ObjectFiles += "$BUILD_DIR\$($file[1])"
    }
}

# TouchGFX应用文件
$TouchGFXAppFiles = @(
    @("CM7/TouchGFX/App/app_touchgfx.c", "app_touchgfx.o")
)

foreach ($file in $TouchGFXAppFiles) {
    if (Compile-File $GCC $C_FLAGS $file[0] $file[1]) {
        $ObjectFiles += "$BUILD_DIR\$($file[1])"
    }
}

# TouchGFX C++文件
$TouchGFXCppFiles = @(
    @("CM7/TouchGFX/target/TouchGFXConfiguration.cpp", "TouchGFXConfiguration.o"),
    @("CM7/TouchGFX/target/TouchGFXHAL.cpp", "TouchGFXHAL.o"),
    @("CM7/TouchGFX/target/generated/TouchGFXGeneratedHAL.cpp", "TouchGFXGeneratedHAL.o"),
    @("CM7/TouchGFX/target/generated/STM32DMA.cpp", "STM32DMA.o"),
    @("CM7/TouchGFX/gui/src/common/FrontendApplication.cpp", "FrontendApplication.o"),
    @("CM7/TouchGFX/gui/src/model/Model.cpp", "Model.o"),
    @("CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp", "Screen1View.o"),
    @("CM7/TouchGFX/gui/src/screen1_screen/Screen1Presenter.cpp", "Screen1Presenter.o"),
    @("CM7/TouchGFX/generated/gui_generated/src/common/FrontendApplicationBase.cpp", "FrontendApplicationBase.o"),
    @("CM7/TouchGFX/generated/gui_generated/src/screen1_screen/Screen1ViewBase.cpp", "Screen1ViewBase.o")
)

foreach ($file in $TouchGFXCppFiles) {
    if (Compile-File $GPP $CPP_FLAGS $file[0] $file[1]) {
        $ObjectFiles += "$BUILD_DIR\$($file[1])"
    }
}

Write-Host "Linking..." -ForegroundColor Green

# 链接标志
$LINKER_FLAGS = "-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -specs=nano.specs -T gcc/STM32H745XIHX_FLASH_CM7.ld -Wl,--gc-sections -Wl,--print-memory-usage -Wl,-Map=$BUILD_DIR/TouchGFX_Fixed.map"

# 使用TouchGFX预编译库
$TOUCHGFX_LIB = "CM7/Middlewares/ST/touchgfx/lib/core/cortex_m7/gcc/libtouchgfx-float-abi-hard.a"

# 链接命令
$LINK_CMD = "$GPP $LINKER_FLAGS $($ObjectFiles -join ' ') `"$TOUCHGFX_LIB`" -lc -lm -lnosys -o `"$BUILD_DIR/TouchGFX_Fixed.elf`""

Write-Host "Executing link command..." -ForegroundColor Cyan
$result = Invoke-Expression $LINK_CMD

if ($LASTEXITCODE -ne 0) {
    Write-Host "FAILED: Linking failed" -ForegroundColor Red
    exit 1
}

Write-Host "Creating HEX file..." -ForegroundColor Green
$HEX_CMD = "$OBJCOPY -O ihex `"$BUILD_DIR/TouchGFX_Fixed.elf`" `"$BUILD_DIR/TouchGFX_Fixed.hex`""
Invoke-Expression $HEX_CMD

Write-Host "Checking size..." -ForegroundColor Green
$SIZE_CMD = "$SIZE `"$BUILD_DIR/TouchGFX_Fixed.elf`""
Invoke-Expression $SIZE_CMD

Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "Output files:" -ForegroundColor Yellow
Write-Host "  ELF: $BUILD_DIR/TouchGFX_Fixed.elf" -ForegroundColor White
Write-Host "  HEX: $BUILD_DIR/TouchGFX_Fixed.hex" -ForegroundColor White
Write-Host "  MAP: $BUILD_DIR/TouchGFX_Fixed.map" -ForegroundColor White
