# TouchGFX固件链接脚本
Write-Host "=== 链接TouchGFX完整固件 ===" -ForegroundColor Green

$GCC = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe"
$OBJCOPY = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-objcopy.exe"
$SIZE = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-size.exe"
$PROGRAMMER = "C:\ST\STM32CubeCLT_1.18.0\STM32CubeProgrammer\bin\STM32_Programmer_CLI.exe"

# 收集所有目标文件
$ObjectFiles = Get-ChildItem "build_touchgfx_complete\*.o" | ForEach-Object { $_.FullName }
Write-Host "找到 $($ObjectFiles.Count) 个目标文件" -ForegroundColor Yellow

Write-Host "开始链接..." -ForegroundColor Yellow

# 构建链接命令
$LinkArgs = @(
    "-mcpu=cortex-m7"
    "-mthumb" 
    "-mfpu=fpv5-sp-d16"
    "-mfloat-abi=hard"
    "--specs=nano.specs"
    "--specs=nosys.specs"
    "-Wl,--gc-sections"
    "-static"
    "-Wl,--start-group"
    "-lc"
    "-lm" 
    "-Wl,--end-group"
    "-Tgcc\STM32H745XIHX_FLASH_CM7.ld"
) + $ObjectFiles + @("-o", "build_touchgfx_complete\TouchGFX_Complete.elf")

# 执行链接
& $GCC $LinkArgs

if ($LASTEXITCODE -eq 0) {
    Write-Host "链接成功!" -ForegroundColor Green
    
    # 生成HEX文件
    Write-Host "生成HEX文件..." -ForegroundColor Yellow
    & $OBJCOPY "-O" "ihex" "build_touchgfx_complete\TouchGFX_Complete.elf" "build_touchgfx_complete\TouchGFX_Complete.hex"
    
    # 生成BIN文件
    Write-Host "生成BIN文件..." -ForegroundColor Yellow  
    & $OBJCOPY "-O" "binary" "build_touchgfx_complete\TouchGFX_Complete.elf" "build_touchgfx_complete\TouchGFX_Complete.bin"
    
    # 显示大小
    Write-Host "程序大小信息:" -ForegroundColor Green
    & $SIZE "build_touchgfx_complete\TouchGFX_Complete.elf"
    
    Write-Host ""
    Write-Host "固件文件已生成:" -ForegroundColor Green
    Write-Host "  ELF: build_touchgfx_complete\TouchGFX_Complete.elf" -ForegroundColor White
    Write-Host "  HEX: build_touchgfx_complete\TouchGFX_Complete.hex" -ForegroundColor White
    Write-Host "  BIN: build_touchgfx_complete\TouchGFX_Complete.bin" -ForegroundColor White
    
    Write-Host ""
    Write-Host "自动下载固件到STM32H745I-DISCO开发板..." -ForegroundColor Yellow
    
    & $PROGRAMMER "-c" "port=SWD" "freq=4000" "-w" "build_touchgfx_complete\TouchGFX_Complete.hex" "-v" "-rst"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "固件下载成功!" -ForegroundColor Green
        Write-Host "TouchGFX界面应该显示，ADC1(PA1)控制右侧仪表盘" -ForegroundColor Cyan
        Write-Host "请在PA1引脚输入0-3.3V电压测试ADC功能" -ForegroundColor Cyan
    } else {
        Write-Host "固件下载失败!" -ForegroundColor Red
    }
} else {
    Write-Host "链接失败!" -ForegroundColor Red
}

Write-Host ""
Write-Host "完成!" -ForegroundColor Green
