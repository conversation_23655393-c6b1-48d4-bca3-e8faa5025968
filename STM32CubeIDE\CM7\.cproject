<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?>
<cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
  <storageModule moduleId="org.eclipse.cdt.core.settings">
    <cconfiguration id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********">
      <storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
        <externalSettings/>
        <extensions>
          <extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
          <extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
        </extensions>
      </storageModule>
      <storageModule moduleId="cdtBuildSystem" version="4.0.0">
        <configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="rm -rf" description="" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********" name="Debug" parent="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug">
          <folderInfo id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********." name="/" resourcePath="">
            <toolChain id="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.debug.1175331840" name="MCU ARM GCC" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.debug">
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu.714445295" name="MCU" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu" value="STM32H745XIHx" valueType="string"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid.1435829034" name="CPU" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid" value="0" valueType="string"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid.1188158285" name="Core" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid" value="0" valueType="string"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.1457706929" name="Floating-point unit" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.value.fpv5-d16" valueType="enumerated"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.475658124" name="Floating-point ABI" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.value.hard" valueType="enumerated"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board.1570139410" name="Board" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board" value="genericBoard" valueType="string"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults.749883907" name="Defaults" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults" value="com.st.stm32cube.ide.common.services.build.inputs.revA.1.0.6 || Debug || true || Executable || com.st.stm32cube.ide.mcu.gnu.managedbuild.option.toolchain.value.workspace || STM32H745XIHx || 0 || 0 || arm-none-eabi- || ${gnu_tools_for_stm32_compiler_path} || ../../../CM7/Core/Inc | ../../../CM7/TouchGFX/App | ../../../CM7/TouchGFX/target/generated | ../../../CM7/TouchGFX/target | ../../../Drivers/BSP/Components/Common | ../../../Drivers/BSP/STM32H745I-DISCO | ../../../Utilities/JPEG | ../../../Drivers/STM32H7xx_HAL_Driver/Inc | ../../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy | ../../../Middlewares/Third_Party/FreeRTOS/Source/include | ../../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 | ../../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F | ../../../Drivers/CMSIS/Device/ST/STM32H7xx/Include | ../../../Drivers/CMSIS/Include || ../../../CM7/Core/Inc | ../../../CM7/TouchGFX/App | ../../../CM7/TouchGFX/target/generated | ../../../CM7/TouchGFX/target | ../../../Drivers/BSP/Components/Common | ../../../Drivers/BSP/STM32H745I-DISCO | ../../../Utilities/JPEG | ../../../Drivers/STM32H7xx_HAL_Driver/Inc | ../../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy | ../../../Middlewares/Third_Party/FreeRTOS/Source/include | ../../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 | ../../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F | ../../../Drivers/CMSIS/Device/ST/STM32H7xx/Include | ../../../Drivers/CMSIS/Include ||  || CORE_CM7 | USE_HAL_DRIVER | STM32H745xx | USE_PWR_DIRECT_SMPS_SUPPLY ||  ||  ||  ||  || ${workspace_loc:/${ProjName}/STM32H745XIHX_FLASH.ld} || true || NonSecure ||  || secure_nsclib.o ||  || None ||  ||  || " valueType="string"/>
              <option id="com.st.stm32cube.ide.mcu.debug.option.cpuclock.1139472231" superClass="com.st.stm32cube.ide.mcu.debug.option.cpuclock" value="200" valueType="string"/>
              <targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform.925443725" isAbstract="false" osList="all" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform"/>
              <builder buildPath="${workspace_loc:/STM32H745I_DISCO_CM7}/Debug" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.builder.67114544" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.builder"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.276952817" name="MCU GCC Assembler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler">
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel.1060700677" name="Debug level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel.value.g3" valueType="enumerated"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.definedsymbols.1153681004" name="Define symbols (-D)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.definedsymbols" valueType="definedSymbols">
                  <listOptionValue builtIn="false" value="DEBUG"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.includepaths.932573753" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.includepaths" valueType="includePath">
                  <listOptionValue builtIn="false" value="../../../CM7/Core/Inc"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/App"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target/generated"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/Components/Common"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/STM32H745I-DISCO"/>
                  <listOptionValue builtIn="false" value="../../../Utilities/JPEG"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/include"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Device/ST/STM32H7xx/Include"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/Middlewares/ST/touchgfx/framework/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/fonts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/gui_generated/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/images/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/texts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/videos/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/gui/include"/>
                </option>
                <inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input.1222631233" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input"/>
              </tool>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.1776235969" name="MCU GCC Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler">
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel.761906184" name="Debug level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel.value.g3" valueType="enumerated"/>
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.optimization.level.979925981" name="Optimization level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.optimization.level"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.definedsymbols.2086541948" name="Define symbols (-D)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.definedsymbols" valueType="definedSymbols">
                  <listOptionValue builtIn="false" value="DEBUG"/>
                  <listOptionValue builtIn="false" value="CORE_CM7"/>
                  <listOptionValue builtIn="false" value="USE_HAL_DRIVER"/>
                  <listOptionValue builtIn="false" value="STM32H745xx"/>
                  <listOptionValue builtIn="false" value="USE_PWR_DIRECT_SMPS_SUPPLY"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths.2123098888" name="Include paths (-I)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths" valueType="includePath">
                  <listOptionValue builtIn="false" value="../../../CM7/Core/Inc"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/App"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target/generated"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/Components/Common"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/STM32H745I-DISCO"/>
                  <listOptionValue builtIn="false" value="../../../Utilities/JPEG"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/include"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Device/ST/STM32H7xx/Include"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/Middlewares/ST/touchgfx/framework/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/fonts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/gui_generated/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/images/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/texts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/videos/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/gui/include"/>
                </option>
                <inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.863552565" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c"/>
              </tool>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.1496895260" name="MCU G++ Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler">
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel.436232503" name="Debug level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel.value.g3" valueType="enumerated"/>
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.optimization.level.124442721" name="Optimization level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.optimization.level"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.definedsymbols.822818912" name="Define symbols (-D)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.definedsymbols" valueType="definedSymbols">
                  <listOptionValue builtIn="false" value="DEBUG"/>
                  <listOptionValue builtIn="false" value="CORE_CM7"/>
                  <listOptionValue builtIn="false" value="USE_HAL_DRIVER"/>
                  <listOptionValue builtIn="false" value="STM32H745xx"/>
                  <listOptionValue builtIn="false" value="USE_PWR_DIRECT_SMPS_SUPPLY"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.includepaths.972932210" name="Include paths (-I)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.includepaths" valueType="includePath">
                  <listOptionValue builtIn="false" value="../../../CM7/Core/Inc"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/App"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target/generated"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/Components/Common"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/STM32H745I-DISCO"/>
                  <listOptionValue builtIn="false" value="../../../Utilities/JPEG"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/include"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Device/ST/STM32H7xx/Include"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/Middlewares/ST/touchgfx/framework/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/fonts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/gui_generated/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/images/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/texts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/videos/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/gui/include"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.otherflags.9011585274" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.otherflags" valueType="stringList">
                  <listOptionValue builtIn="false" value="-femit-class-debug-always"/>
                </option>
                <inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.input.cpp.266443010" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.input.cpp"/>
              </tool>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.1821325327" name="MCU GCC Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.29222013" name="MCU G++ Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker">
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.script.443349220" name="Linker Script (-T)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.script" value="${workspace_loc:/${ProjName}/STM32H745XIHX_FLASH.ld}" valueType="string"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.directories.1161748005" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.directories" valueType="libPaths">
                  <listOptionValue builtIn="false" value="../../../CM7/Middlewares/ST/touchgfx/lib/core/cortex_m7/gcc"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.libraries.5382811784" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.libraries" valueType="libs">
                  <listOptionValue builtIn="false" value=":libtouchgfx-float-abi-hard.a"/>
                </option>
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.additionalobjs.1161748005" name="Additional object files" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.additionalobjs" useByScannerDiscovery="false"/>
                <inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.input.674564790" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.input">
                  <additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
                  <additionalInput kind="additionalinput" paths="$(LIBS)"/>
                </inputType>
              </tool>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.529977468" name="MCU GCC Archiver" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.543899809" name="MCU Size" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.2147118698" name="MCU Output Converter list file" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.1841397041" name="MCU Output Converter Hex" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.1990067762" name="MCU Output Converter Binary" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.217018711" name="MCU Output Converter Verilog" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.1457202048" name="MCU Output Converter Motorola S-rec" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.1017677457" name="MCU Output Converter Motorola S-rec with symbols" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec"/>
            </toolChain>
          </folderInfo>
        </configuration>
      </storageModule>
      <storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
    </cconfiguration>
    <cconfiguration id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.**********">
      <storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.**********" moduleId="org.eclipse.cdt.core.settings" name="Release">
        <externalSettings/>
        <extensions>
          <extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
          <extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
        </extensions>
      </storageModule>
      <storageModule moduleId="cdtBuildSystem" version="4.0.0">
        <configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" cleanCommand="rm -rf" description="" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.**********" name="Release" parent="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release">
          <folderInfo id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.**********." name="/" resourcePath="">
            <toolChain id="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.release.921758675" name="MCU ARM GCC" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.release">
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu.902306329" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu" value="STM32H745XIHx" valueType="string"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid.578061198" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid" value="0" valueType="string"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid.1063628473" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid" value="0" valueType="string"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.1510044563" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.value.fpv5-d16" valueType="enumerated"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.1681113347" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.value.hard" valueType="enumerated"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board.355560849" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board" value="genericBoard" valueType="string"/>
              <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults.1024935638" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults" value="com.st.stm32cube.ide.common.services.build.inputs.revA.1.0.6 || Release || false || Executable || com.st.stm32cube.ide.mcu.gnu.managedbuild.option.toolchain.value.workspace || STM32H745XIHx || 0 || 0 || arm-none-eabi- || ${gnu_tools_for_stm32_compiler_path} || ../../../CM7/Core/Inc | ../../../CM7/TouchGFX/App | ../../../CM7/TouchGFX/target/generated | ../../../CM7/TouchGFX/target | ../../../Drivers/BSP/Components/Common | ../../../Drivers/BSP/STM32H745I-DISCO | ../../../Utilities/JPEG | ../../../Drivers/STM32H7xx_HAL_Driver/Inc | ../../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy | ../../../Middlewares/Third_Party/FreeRTOS/Source/include | ../../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 | ../../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F | ../../../Drivers/CMSIS/Device/ST/STM32H7xx/Include | ../../../Drivers/CMSIS/Include || ../../../CM7/Core/Inc | ../../../CM7/TouchGFX/App | ../../../CM7/TouchGFX/target/generated | ../../../CM7/TouchGFX/target | ../../../Drivers/BSP/Components/Common | ../../../Drivers/BSP/STM32H745I-DISCO | ../../../Utilities/JPEG | ../../../Drivers/STM32H7xx_HAL_Driver/Inc | ../../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy | ../../../Middlewares/Third_Party/FreeRTOS/Source/include | ../../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 | ../../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F | ../../../Drivers/CMSIS/Device/ST/STM32H7xx/Include | ../../../Drivers/CMSIS/Include ||  || CORE_CM7 | USE_HAL_DRIVER | STM32H745xx | USE_PWR_DIRECT_SMPS_SUPPLY ||  ||  ||  ||  || ${workspace_loc:/${ProjName}/STM32H745XIHX_FLASH.ld} || true || NonSecure ||  || secure_nsclib.o ||  || None ||  ||  || " valueType="string"/>
              <option id="com.st.stm32cube.ide.mcu.debug.option.cpuclock.1806125998" superClass="com.st.stm32cube.ide.mcu.debug.option.cpuclock" value="200" valueType="string"/>
              <targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform.1154539210" isAbstract="false" osList="all" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform"/>
              <builder buildPath="${workspace_loc:/STM32H745I_DISCO_CM7}/Release" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.builder.2139488191" managedBuildOn="true" name="Gnu Make Builder.Release" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.builder"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.1713761685" name="MCU GCC Assembler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler">
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel.106199201" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel.value.g0" valueType="enumerated"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.includepaths.410585023" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.includepaths" valueType="includePath">
                  <listOptionValue builtIn="false" value="../../../CM7/Core/Inc"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/App"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target/generated"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/Components/Common"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/STM32H745I-DISCO"/>
                  <listOptionValue builtIn="false" value="../../../Utilities/JPEG"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/include"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Device/ST/STM32H7xx/Include"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/Middlewares/ST/touchgfx/framework/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/fonts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/gui_generated/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/images/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/texts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/videos/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/gui/include"/>
                </option>
                <inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input.798927887" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input"/>
              </tool>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.677363362" name="MCU GCC Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler">
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel.788005120" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel.value.g0" valueType="enumerated"/>
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.optimization.level.23318844" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.optimization.level" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.optimization.level.value.os" valueType="enumerated"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.definedsymbols.1933690480" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.definedsymbols" valueType="definedSymbols">
                  <listOptionValue builtIn="false" value="CORE_CM7"/>
                  <listOptionValue builtIn="false" value="USE_HAL_DRIVER"/>
                  <listOptionValue builtIn="false" value="STM32H745xx"/>
                  <listOptionValue builtIn="false" value="USE_PWR_DIRECT_SMPS_SUPPLY"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths.1025436377" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths" valueType="includePath">
                  <listOptionValue builtIn="false" value="../../../CM7/Core/Inc"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/App"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target/generated"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/Components/Common"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/STM32H745I-DISCO"/>
                  <listOptionValue builtIn="false" value="../../../Utilities/JPEG"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/include"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Device/ST/STM32H7xx/Include"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/Middlewares/ST/touchgfx/framework/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/fonts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/gui_generated/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/images/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/texts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/videos/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/gui/include"/>
                </option>
                <inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.**********" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c"/>
              </tool>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.**********" name="MCU G++ Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler">
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel.373828990" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel.value.g0" valueType="enumerated"/>
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.optimization.level.380498840" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.optimization.level" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.optimization.level.value.os" valueType="enumerated"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.definedsymbols.767778986" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.definedsymbols" valueType="definedSymbols">
                  <listOptionValue builtIn="false" value="CORE_CM7"/>
                  <listOptionValue builtIn="false" value="USE_HAL_DRIVER"/>
                  <listOptionValue builtIn="false" value="STM32H745xx"/>
                  <listOptionValue builtIn="false" value="USE_PWR_DIRECT_SMPS_SUPPLY"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.includepaths.455745858" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.includepaths" valueType="includePath">
                  <listOptionValue builtIn="false" value="../../../CM7/Core/Inc"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/App"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target/generated"/>
                  <listOptionValue builtIn="false" value="../../../CM7/TouchGFX/target"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/Components/Common"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/BSP/STM32H745I-DISCO"/>
                  <listOptionValue builtIn="false" value="../../../Utilities/JPEG"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/include"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2"/>
                  <listOptionValue builtIn="false" value="../../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Device/ST/STM32H7xx/Include"/>
                  <listOptionValue builtIn="false" value="../../../Drivers/CMSIS/Include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/Middlewares/ST/touchgfx/framework/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/fonts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/gui_generated/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/images/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/texts/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/generated/videos/include"/>
                  <listOptionValue builtIn="false" value="../../CM7/../../CM7/TouchGFX/gui/include"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.otherflags.9242119105" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.otherflags" valueType="stringList">
                  <listOptionValue builtIn="false" value="-femit-class-debug-always"/>
                </option>
                <inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.input.cpp.255080198" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.input.cpp"/>
              </tool>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.1839309305" name="MCU GCC Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.2023185281" name="MCU G++ Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker">
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.script.348935113" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.script" value="${workspace_loc:/${ProjName}/STM32H745XIHX_FLASH.ld}" valueType="string"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.directories.9011585274" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.directories" valueType="libPaths">
                  <listOptionValue builtIn="false" value="../../../CM7/Middlewares/ST/touchgfx/lib/core/cortex_m7/gcc"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.libraries.9242119105" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.libraries" valueType="libs">
                  <listOptionValue builtIn="false" value=":libtouchgfx-float-abi-hard.a"/>
                </option>
                <option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.additionalobjs.5382811784" name="Additional object files" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.option.additionalobjs" useByScannerDiscovery="false"/>
                <inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.input.1492543103" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.input">
                  <additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
                  <additionalInput kind="additionalinput" paths="$(LIBS)"/>
                </inputType>
              </tool>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.1444063485" name="MCU GCC Archiver" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.433117712" name="MCU Size" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.2059174679" name="MCU Output Converter list file" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.1578838563" name="MCU Output Converter Hex" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.589678685" name="MCU Output Converter Binary" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.999265885" name="MCU Output Converter Verilog" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.1020623882" name="MCU Output Converter Motorola S-rec" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec"/>
              <tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.1621056479" name="MCU Output Converter Motorola S-rec with symbols" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec"/>
            </toolChain>
          </folderInfo>
        </configuration>
      </storageModule>
      <storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
    </cconfiguration>
  </storageModule>
  <storageModule moduleId="org.eclipse.cdt.core.pathentry"/>
  <storageModule moduleId="cdtBuildSystem" version="4.0.0">
    <project id="STM32H745I_DISCO_CM7.null.**********" name="STM32H745I_DISCO_CM7"/>
  </storageModule>
  <storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
  <storageModule moduleId="scannerConfiguration">
    <autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
    <scannerConfigBuildInfo instanceId="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.**********;com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.**********.;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.**********;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.input.cpp.255080198">
      <autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
    </scannerConfigBuildInfo>
    <scannerConfigBuildInfo instanceId="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.**********;com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.**********.;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.677363362;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.**********">
      <autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
    </scannerConfigBuildInfo>
    <scannerConfigBuildInfo instanceId="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********;com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********.;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.1776235969;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.863552565">
      <autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
    </scannerConfigBuildInfo>
    <scannerConfigBuildInfo instanceId="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********;com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********.;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.1496895260;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.input.cpp.266443010">
      <autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
    </scannerConfigBuildInfo>
  </storageModule>
  <storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
</cproject>
