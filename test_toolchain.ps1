# Test Toolchain
Write-Host "Testing toolchain..." -ForegroundColor Yellow

$TOOLCHAIN_PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin"
$GCC = "${TOOLCHAIN_PATH}\arm-none-eabi-gcc.exe"

Write-Host "Toolchain path: $TOOLCHAIN_PATH" -ForegroundColor Cyan
Write-Host "GCC path: $GCC" -ForegroundColor Cyan

if (Test-Path $GCC) {
    Write-Host "GCC found!" -ForegroundColor Green
    & $GCC --version
} else {
    Write-Host "GCC not found!" -ForegroundColor Red
}
