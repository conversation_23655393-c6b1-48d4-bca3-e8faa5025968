@echo off
echo 开始编译TouchGFX项目...

REM 设置工具链路径
set TOOLCHAIN_PATH=C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin
set GCC=%TOOLCHAIN_PATH%\arm-none-eabi-gcc.exe
set OBJCOPY=%TOOLCHAIN_PATH%\arm-none-eabi-objcopy.exe
set SIZE=%TOOLCHAIN_PATH%\arm-none-eabi-size.exe

REM 编译器标志
set CFLAGS=-mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_FULL_LL_DRIVER -DDATA_IN_D2_SRAM -O2 -Wall -fdata-sections -ffunction-sections -g -gdwarf-2

REM 包含路径
set INCLUDES=-ICM7/Core/Inc -ICM7/TouchGFX/App -ICM7/TouchGFX/target/generated -ICM7/TouchGFX/target -IDrivers/STM32H7xx_HAL_Driver/Inc -IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -IMiddlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1 -IDrivers/CMSIS/Device/ST/STM32H7xx/Include -IDrivers/CMSIS/Include -IMiddlewares/ST/touchgfx/framework/include -ICM7/TouchGFX/generated/fonts/include -ICM7/TouchGFX/generated/gui_generated/include -ICM7/TouchGFX/generated/images/include -ICM7/TouchGFX/generated/texts/include -ICM7/TouchGFX/gui/include -IDrivers/BSP/STM32H745I-DISCO -IDrivers/BSP/Components/Common -IDrivers/BSP/Components/otm8009a -IDrivers/BSP/Components/ft5336

REM 链接脚本
set LDSCRIPT=CM7/STM32H745ZITX_FLASH.ld

REM TouchGFX预编译库
set TOUCHGFX_LIB=Middlewares/ST/touchgfx/lib/core/cortex_m7/gcc/libtouchgfx-float-abi-hard.a

REM 链接器标志
set LDFLAGS=-mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -specs=nano.specs -T%LDSCRIPT% -lc -lm -lnosys -Wl,-Map=build/output.map,--cref -Wl,--gc-sections -static --specs=rdimon.specs

REM 创建构建目录
if not exist build mkdir build

echo 编译源文件...

REM 编译核心源文件
echo 编译核心源文件...
"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/Core/Src/main.c" -o "build/main.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/Core/Src/stm32h7xx_it.c" -o "build/stm32h7xx_it.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/Core/Src/stm32h7xx_hal_msp.c" -o "build/stm32h7xx_hal_msp.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/Core/Src/stm32h7xx_hal_timebase_tim.c" -o "build/stm32h7xx_hal_timebase_tim.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/Core/Src/system_stm32h7xx.c" -o "build/system_stm32h7xx.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/Core/Src/sysmem.c" -o "build/sysmem.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/Core/Src/syscalls.c" -o "build/syscalls.o"
if %errorlevel% neq 0 goto :error

REM 编译TouchGFX源文件
echo 编译TouchGFX源文件...
"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/App/app_touchgfx.c" -o "build/app_touchgfx.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/target/generated/TouchGFXConfiguration.cpp" -o "build/TouchGFXConfiguration.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/target/generated/TouchGFXGeneratedHAL.cpp" -o "build/TouchGFXGeneratedHAL.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/target/STM32TouchController.cpp" -o "build/STM32TouchController.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/target/TouchGFXHAL.cpp" -o "build/TouchGFXHAL.o"
if %errorlevel% neq 0 goto :error

REM 编译TouchGFX GUI源文件
echo 编译TouchGFX GUI源文件...
"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/generated/gui_generated/src/common/FrontendApplicationBase.cpp" -o "build/FrontendApplicationBase.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/generated/gui_generated/src/screen1_screen/Screen1ViewBase.cpp" -o "build/Screen1ViewBase.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/generated/gui_generated/src/screen1_screen/Screen1PresenterBase.cpp" -o "build/Screen1PresenterBase.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/gui/src/common/FrontendApplication.cpp" -o "build/FrontendApplication.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/gui/src/model/Model.cpp" -o "build/Model.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp" -o "build/Screen1View.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "CM7/TouchGFX/gui/src/screen1_screen/Screen1Presenter.cpp" -o "build/Screen1Presenter.o"
if %errorlevel% neq 0 goto :error

REM 编译关键HAL驱动源文件
echo 编译HAL驱动源文件...
"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c" -o "build/stm32h7xx_hal.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c" -o "build/stm32h7xx_hal_adc.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c" -o "build/stm32h7xx_hal_adc_ex.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c" -o "build/stm32h7xx_hal_cortex.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.c" -o "build/stm32h7xx_hal_crc.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c" -o "build/stm32h7xx_hal_dma.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c" -o "build/stm32h7xx_hal_dma_ex.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.c" -o "build/stm32h7xx_hal_dma2d.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dsi.c" -o "build/stm32h7xx_hal_dsi.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c" -o "build/stm32h7xx_hal_exti.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c" -o "build/stm32h7xx_hal_flash.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c" -o "build/stm32h7xx_hal_flash_ex.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c" -o "build/stm32h7xx_hal_gpio.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c" -o "build/stm32h7xx_hal_hsem.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c" -o "build/stm32h7xx_hal_i2c.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c" -o "build/stm32h7xx_hal_i2c_ex.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.c" -o "build/stm32h7xx_hal_ltdc.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.c" -o "build/stm32h7xx_hal_ltdc_ex.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c" -o "build/stm32h7xx_hal_mdma.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c" -o "build/stm32h7xx_hal_pwr.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c" -o "build/stm32h7xx_hal_pwr_ex.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.c" -o "build/stm32h7xx_hal_qspi.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c" -o "build/stm32h7xx_hal_rcc.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c" -o "build/stm32h7xx_hal_rcc_ex.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.c" -o "build/stm32h7xx_hal_sdram.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c" -o "build/stm32h7xx_hal_tim.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c" -o "build/stm32h7xx_hal_tim_ex.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.c" -o "build/stm32h7xx_ll_fmc.o"
if %errorlevel% neq 0 goto :error

REM 编译FreeRTOS源文件
echo 编译FreeRTOS源文件...
"%GCC%" %CFLAGS% %INCLUDES% -c "Middlewares/Third_Party/FreeRTOS/Source/croutine.c" -o "build/croutine.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Middlewares/Third_Party/FreeRTOS/Source/event_groups.c" -o "build/event_groups.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Middlewares/Third_Party/FreeRTOS/Source/list.c" -o "build/list.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Middlewares/Third_Party/FreeRTOS/Source/queue.c" -o "build/queue.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c" -o "build/stream_buffer.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Middlewares/Third_Party/FreeRTOS/Source/tasks.c" -o "build/tasks.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Middlewares/Third_Party/FreeRTOS/Source/timers.c" -o "build/timers.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c" -o "build/cmsis_os2.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1/port.c" -o "build/port.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c" -o "build/heap_4.o"
if %errorlevel% neq 0 goto :error

REM 编译BSP源文件
echo 编译BSP源文件...
"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery.c" -o "build/stm32h745i_discovery.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_lcd.c" -o "build/stm32h745i_discovery_lcd.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_sdram.c" -o "build/stm32h745i_discovery_sdram.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_ts.c" -o "build/stm32h745i_discovery_ts.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/BSP/Components/otm8009a/otm8009a.c" -o "build/otm8009a.o"
if %errorlevel% neq 0 goto :error

"%GCC%" %CFLAGS% %INCLUDES% -c "Drivers/BSP/Components/ft5336/ft5336.c" -o "build/ft5336.o"
if %errorlevel% neq 0 goto :error

REM 编译汇编文件
echo 编译汇编文件...
"%GCC%" %CFLAGS% -c "CM7/Core/Startup/startup_stm32h745zitx.s" -o "build/startup_stm32h745zitx.o"
if %errorlevel% neq 0 goto :error

echo 链接...
REM 链接生成ELF文件
"%GCC%" build/*.o "%TOUCHGFX_LIB%" %LDFLAGS% -o "build/TouchGFX_Complete.elf"
if %errorlevel% neq 0 goto :error

echo 生成HEX文件...
REM 生成HEX文件
"%OBJCOPY%" -O ihex "build/TouchGFX_Complete.elf" "build/TouchGFX_Complete.hex"
if %errorlevel% neq 0 goto :error

echo 程序大小信息:
"%SIZE%" "build/TouchGFX_Complete.elf"

echo 编译完成! 输出文件:
echo   ELF: build/TouchGFX_Complete.elf
echo   HEX: build/TouchGFX_Complete.hex

set /p download="是否下载到STM32H745I-DISCO开发板? (y/n): "
if /i "%download%"=="y" (
    echo 下载固件到开发板...
    "C:\ST\STM32CubeCLT_1.18.0\STM32CubeProgrammer\bin\STM32_Programmer_CLI.exe" -c port=SWD -w "build/TouchGFX_Complete.hex" 0x08000000 -v -rst
    if %errorlevel% equ 0 (
        echo 固件下载成功!
        echo 开发板应该显示TouchGFX界面，ADC1(PA1)控制右侧仪表盘
    ) else (
        echo 固件下载失败!
    )
)

pause
exit /b 0

:error
echo 编译失败!
pause
exit /b 1
