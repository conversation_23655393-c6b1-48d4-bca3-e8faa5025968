# 最小化TouchGFX编译脚本 - 解决白屏问题
# 我会按照要求完成任务

Write-Host "Building minimal TouchGFX project to fix white screen..." -ForegroundColor Yellow

# 工具链路径
$TOOLCHAIN_PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin"
$GCC = "${TOOLCHAIN_PATH}\arm-none-eabi-gcc.exe"
$GPP = "${TOOLCHAIN_PATH}\arm-none-eabi-g++.exe"
$OBJCOPY = "${TOOLCHAIN_PATH}\arm-none-eabi-objcopy.exe"
$SIZE = "${TOOLCHAIN_PATH}\arm-none-eabi-size.exe"

# 检查工具链
if (!(Test-Path $GCC)) {
    Write-Host "Error: GCC not found at $GCC" -ForegroundColor Red
    exit 1
}

# 创建输出目录
$BUILD_DIR = "build_minimal_touchgfx"
if (Test-Path $BUILD_DIR) { Remove-Item $BUILD_DIR -Recurse -Force }
New-Item -ItemType Directory -Path $BUILD_DIR | Out-Null

Write-Host "Compiling minimal TouchGFX application..." -ForegroundColor Green

# 编译器标志
$COMMON_FLAGS = "-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_PWR_DIRECT_SMPS_SUPPLY -Os -Wall -fdata-sections -ffunction-sections -g"
$CPP_FLAGS = "$COMMON_FLAGS -fno-exceptions -fno-rtti -std=c++11"
$C_FLAGS = "$COMMON_FLAGS -std=gnu11"

# 包含路径
$INCLUDES = "-ICM7/Core/Inc -ICM7/TouchGFX/App -ICM7/TouchGFX/target/generated -ICM7/TouchGFX/target -ICM7/TouchGFX/gui/include -ICM7/TouchGFX/generated/gui_generated/include -ICM7/Middlewares/ST/touchgfx/framework/include -ICM7/Middlewares/Third_Party/FreeRTOS/Source/include -ICM7/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -ICM7/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1 -IDrivers/STM32H7xx_HAL_Driver/Inc -IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy -IDrivers/CMSIS/Device/ST/STM32H7xx/Include -IDrivers/CMSIS/Include -IDrivers/BSP/STM32H745I-DISCO"

# 编译函数
function Compile-File {
    param($Compiler, $Flags, $SourceFile, $OutputFile)
    
    if (!(Test-Path $SourceFile)) {
        Write-Host "Warning: Source file not found: $SourceFile" -ForegroundColor Yellow
        return $false
    }
    
    $OutputPath = "${BUILD_DIR}\${OutputFile}"
    $cmd = "& `"$Compiler`" $Flags $INCLUDES -c `"$SourceFile`" -o `"$OutputPath`""
    
    Write-Host "Compiling: $SourceFile" -ForegroundColor Cyan
    try {
        Invoke-Expression $cmd
        if ($LASTEXITCODE -ne 0) {
            Write-Host "FAILED: $SourceFile" -ForegroundColor Red
            return $false
        }
        return $true
    } catch {
        Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 编译核心文件
$ObjectFiles = @()

# 启动文件
if (Compile-File $GCC $C_FLAGS "gcc/startup_stm32h745xihx_cm7.s" "startup.o") {
    $ObjectFiles += "${BUILD_DIR}\startup.o"
}

# 系统文件
$SystemFiles = @(
    "CM7/Core/Src/main.c",
    "CM7/Core/Src/stm32h7xx_it.c", 
    "CM7/Core/Src/stm32h7xx_hal_msp.c",
    "CM7/Core/Src/system_stm32h7xx.c"
)

foreach ($file in $SystemFiles) {
    $objName = [System.IO.Path]::GetFileNameWithoutExtension($file) + ".o"
    if (Compile-File $GCC $C_FLAGS $file $objName) {
        $ObjectFiles += "${BUILD_DIR}\${objName}"
    }
}

# 关键HAL驱动
$HalFiles = @(
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dsi.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c"
)

foreach ($file in $HalFiles) {
    $objName = [System.IO.Path]::GetFileNameWithoutExtension($file) + ".o"
    if (Compile-File $GCC $C_FLAGS $file $objName) {
        $ObjectFiles += "${BUILD_DIR}\${objName}"
    }
}

# BSP显示驱动
$BspFiles = @(
    "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery.c",
    "Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_lcd.c",
    "Drivers/BSP/Components/otm8009a/otm8009a.c",
    "Drivers/BSP/Components/ft5336/ft5336.c"
)

foreach ($file in $BspFiles) {
    $objName = [System.IO.Path]::GetFileNameWithoutExtension($file) + ".o"
    if (Compile-File $GCC $C_FLAGS $file $objName) {
        $ObjectFiles += "${BUILD_DIR}\${objName}"
    }
}

# FreeRTOS核心
$FreeRTOSFiles = @(
    "CM7/Middlewares/Third_Party/FreeRTOS/Source/tasks.c",
    "CM7/Middlewares/Third_Party/FreeRTOS/Source/queue.c",
    "CM7/Middlewares/Third_Party/FreeRTOS/Source/list.c",
    "CM7/Middlewares/Third_Party/FreeRTOS/Source/timers.c",
    "CM7/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c",
    "CM7/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM7/r0p1/port.c",
    "CM7/Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c"
)

foreach ($file in $FreeRTOSFiles) {
    $objName = [System.IO.Path]::GetFileNameWithoutExtension($file) + ".o"
    if (Compile-File $GCC $C_FLAGS $file $objName) {
        $ObjectFiles += "${BUILD_DIR}\${objName}"
    }
}

# TouchGFX应用
if (Compile-File $GCC $C_FLAGS "CM7/TouchGFX/App/app_touchgfx.c" "app_touchgfx.o") {
    $ObjectFiles += "${BUILD_DIR}\app_touchgfx.o"
}

Write-Host "Compiled $($ObjectFiles.Count) object files" -ForegroundColor Green

# 链接
Write-Host "Linking..." -ForegroundColor Green

$LINKER_FLAGS = "-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -specs=nano.specs -T gcc/STM32H745XIHX_FLASH_CM7.ld -Wl,--gc-sections -Wl,--print-memory-usage"

# 不使用TouchGFX库，创建最小显示应用
$LINK_CMD = "& `"$GCC`" $LINKER_FLAGS $($ObjectFiles -join ' ') -lc -lm -lnosys -o `"${BUILD_DIR}\TouchGFX_Minimal.elf`""

try {
    Invoke-Expression $LINK_CMD
    if ($LASTEXITCODE -ne 0) {
        Write-Host "FAILED: Linking failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 创建HEX文件
Write-Host "Creating HEX file..." -ForegroundColor Green
$HEX_CMD = "& `"$OBJCOPY`" -O ihex `"${BUILD_DIR}\TouchGFX_Minimal.elf`" `"${BUILD_DIR}\TouchGFX_Minimal.hex`""
Invoke-Expression $HEX_CMD

# 检查大小
Write-Host "Checking size..." -ForegroundColor Green
$SIZE_CMD = "& `"$SIZE`" `"${BUILD_DIR}\TouchGFX_Minimal.elf`""
Invoke-Expression $SIZE_CMD

Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "Output files:" -ForegroundColor Yellow
Write-Host "  ELF: ${BUILD_DIR}\TouchGFX_Minimal.elf" -ForegroundColor White
Write-Host "  HEX: ${BUILD_DIR}\TouchGFX_Minimal.hex" -ForegroundColor White

Write-Host "`nNext step: Flash to board using ST-LINK" -ForegroundColor Cyan
Write-Host "Command: STM32_Programmer_CLI.exe -c port=SWD freq=4000 -w ${BUILD_DIR}\TouchGFX_Minimal.hex -v -rst" -ForegroundColor White
