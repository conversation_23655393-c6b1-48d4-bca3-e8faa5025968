/**
  ******************************************************************************
  * @file    stm32h7xx_ll_delayblock.c
  * <AUTHOR> Application Team
  * @brief   DelayBlock Low Layer HAL module driver.
  *          This file provides firmware functions to manage the following
  *          functionalities of the DelayBlock peripheral:
  *           + input clock frequency range 25MHz to 208MHz
  *           + Up to 12 oversampling phases
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2017 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stm32h7xx_hal.h"

/** @addtogroup STM32H7xx_HAL_Driver
  * @{
  */

/** @defgroup DELAYBLOCK_LL DELAYBLOCK_LL
  * @brief Low layer module for Delay Block
  * @{
  */

#if defined (DLYB_SDMMC1) || defined (DLYB_SDMMC2) || defined (DLYB_QUADSPI) || defined (DLYB_OCTOSPI1) || defined (DLYB_OCTOSPI2)

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/** @defgroup DELAYBLOCK_LL_Private_Defines DELAYBLOCK_LL Private Defines
  * @{
  */
#define DLYB_TIMEOUT 0xFFU
/**
  * @}
  */

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

/** @defgroup DELAYBLOCK_LL_Exported_Functions DELAYBLOCK_LL Exported Functions
  * @{
  */

/** @defgroup HAL_DELAYBLOCK_LL_Group1 Initialization/de-initialization functions
  * @brief    Initialization and Configuration functions
  * @{
  */

/**
  * @brief  Enable the Delay Block
  * @param  DLYBx: Pointer to DLYB instance.
  * @retval HAL status
  */
HAL_StatusTypeDef DelayBlock_Enable(DLYB_TypeDef *DLYBx)
{
  DLYBx->CR = DLYB_CR_DEN;
  return HAL_OK;
}

/**
  * @brief  Disable the Delay Block
  * @param  DLYBx: Pointer to DLYB instance.
  * @retval HAL status
  */
HAL_StatusTypeDef DelayBlock_Disable(DLYB_TypeDef *DLYBx)
{
  DLYBx->CR = 0U;
  return HAL_OK;
}

/**
  * @}
  */

/**
  * @}
  */

#endif /* DLYB_SDMMC1 || DLYB_SDMMC2 || DLYB_QUADSPI || DLYB_OCTOSPI1 || DLYB_OCTOSPI2 */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
