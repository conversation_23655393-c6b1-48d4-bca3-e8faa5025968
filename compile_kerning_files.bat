@echo off
echo Compiling TouchGFX kerning and VectorFont files...

REM Set toolchain path
set TOOLCHAIN_PATH=C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin
set GCC=%TOOLCHAIN_PATH%\arm-none-eabi-gcc.exe

echo Using toolchain: %TOOLCHAIN_PATH%

REM Compile kerning files
echo Compiling kerning files...
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/TouchGFX/generated/fonts/include -ICM7/Middlewares/ST/touchgfx/framework/include -c "CM7/TouchGFX/generated/fonts/src/Kerning_verdana_10_4bpp.cpp" -o "build_touchgfx_complete/Kerning_verdana_10_4bpp.o"
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/TouchGFX/generated/fonts/include -ICM7/Middlewares/ST/touchgfx/framework/include -c "CM7/TouchGFX/generated/fonts/src/Kerning_verdana_20_4bpp.cpp" -o "build_touchgfx_complete/Kerning_verdana_20_4bpp.o"
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/TouchGFX/generated/fonts/include -ICM7/Middlewares/ST/touchgfx/framework/include -c "CM7/TouchGFX/generated/fonts/src/Kerning_verdana_40_4bpp.cpp" -o "build_touchgfx_complete/Kerning_verdana_40_4bpp.o"

REM Compile VectorFontRendererBuffers
echo Compiling VectorFontRendererBuffers...
"%GCC%" -mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 -O2 -ICM7/TouchGFX/generated/fonts/include -ICM7/Middlewares/ST/touchgfx/framework/include -c "CM7/TouchGFX/generated/fonts/src/VectorFontRendererBuffers.cpp" -o "build_touchgfx_complete/VectorFontRendererBuffers.o"

if %ERRORLEVEL% equ 0 (
    echo All kerning and VectorFont files compiled successfully!
    echo Ready for final linking with real kerning data...
) else (
    echo Kerning compilation failed!
    pause
    exit /b 1
)

pause
