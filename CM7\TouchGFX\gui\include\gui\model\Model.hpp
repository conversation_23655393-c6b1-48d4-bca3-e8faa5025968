#ifndef MODEL_HPP
#define MODEL_HPP

#include <cstdint>

class ModelListener;

class Model
{
public:
    Model();

    void bind(ModelListener* listener)
    {
        modelListener = listener;
    }

    void tick();

    // ADC数据处理方法
    void processADC1Data();
    uint8_t convertADC1ToGaugeValue(uint16_t adc_average);

protected:
    ModelListener* modelListener;

private:
    // 更新计数器 - 控制仪表盘更新频率
    uint32_t update_counter;
    static const uint32_t UPDATE_INTERVAL = 10; // 每10个tick更新一次(约100ms)
};

#endif // MODEL_HPP
