# STM32H745I TouchGFX Complete Build Script with All Dependencies
Write-Host "=== STM32H745I TouchGFX Complete Build ===" -ForegroundColor Green

# Set environment variables
$env:PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin;C:\ST\STM32CubeCLT_1.18.0\STM32_Programmer_CLI\bin;" + $env:PATH

# Define tools
$GCC = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe"
$GPP = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-g++.exe"
$OBJCOPY = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-objcopy.exe"
$SIZE = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-size.exe"
$STLINK = "C:\ST\STM32CubeCLT_1.18.0\STM32_Programmer_CLI\bin\STM32_Programmer_CLI.exe"

# Common compiler flags
$COMMON_FLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb", 
    "-mfpu=fpv5-sp-d16",
    "-mfloat-abi=hard",
    "-DUSE_HAL_DRIVER",
    "-DSTM32H745xx", 
    "-DCORE_CM7",
    "-DUSE_BPP=16"
)

# Include paths
$TOUCHGFX_PATH = "C:\Users\<USER>\STMicroelectronics\TouchGFX\4.25.0\touchgfx\framework\include"
$INCLUDE_PATHS = @(
    "-ICM7/Core/Inc",
    "-ICM7/TouchGFX/App",
    "-ICM7/TouchGFX/target/generated", 
    "-ICM7/TouchGFX/target",
    "-ICM7/TouchGFX/gui/include",
    "-ICM7/TouchGFX/generated/gui_generated/include",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy",
    "-IDrivers/CMSIS/Device/ST/STM32H7xx/Include",
    "-IDrivers/CMSIS/Include",
    "-IDrivers/BSP/STM32H745I-DISCO",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/include",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F",
    "-I$TOUCHGFX_PATH"
)

# Compiler flags
$CFLAGS = $COMMON_FLAGS + @("--specs=nano.specs", "-Os", "-Wall", "-fdata-sections", "-ffunction-sections", "-g3", "-std=gnu11")
$CPPFLAGS = $COMMON_FLAGS + @("--specs=nano.specs", "-Os", "-Wall", "-fdata-sections", "-ffunction-sections", "-fno-exceptions", "-fno-rtti", "-g3", "-std=c++17")

# Linker flags
$LDFLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb",
    "-mfpu=fpv5-sp-d16", 
    "-mfloat-abi=hard",
    "--specs=nano.specs",
    "--specs=nosys.specs",
    "-Wl,--gc-sections",
    "-static",
    "-Wl,--start-group",
    "-lc",
    "-lm",
    "-Wl,--end-group"
)

# Create build directory
$BUILD_DIR = "build_complete"
if (Test-Path $BUILD_DIR) {
    Remove-Item -Recurse -Force $BUILD_DIR
}
New-Item -ItemType Directory -Path $BUILD_DIR | Out-Null

Write-Host "Building complete project with all dependencies..." -ForegroundColor Yellow

# Function to compile files
function Compile-SourceFile {
    param($Compiler, $Flags, $SourceFile, $OutputFile, $FileType)
    
    if (!(Test-Path $SourceFile)) {
        Write-Host "File not found: $SourceFile" -ForegroundColor Yellow
        return $false
    }
    
    Write-Host "Compiling $FileType`: $SourceFile"
    $cmd_args = $Flags + $INCLUDE_PATHS + @("-c", $SourceFile, "-o", "$BUILD_DIR/$OutputFile")
    
    & $Compiler @cmd_args 2>&1 | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  SUCCESS: $OutputFile" -ForegroundColor Green
        return $true
    } else {
        Write-Host "  FAILED: $SourceFile" -ForegroundColor Red
        return $false
    }
}

$SuccessCount = 0
$ErrorCount = 0
$ObjectFiles = @()

# All source files needed for complete build
$AllSourceFiles = @(
    # Startup and system files
    @("gcc/startup_stm32h745xihx_cm7.s", "startup.o", "ASM"),
    @("Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.c", "system_stm32h7xx.o", "C"),
    
    # Core application files
    @("CM7/Core/Src/main.c", "main.o", "C"),
    @("CM7/Core/Src/stm32h7xx_it.c", "stm32h7xx_it.o", "C"),
    @("CM7/Core/Src/stm32h7xx_hal_msp.c", "stm32h7xx_hal_msp.o", "C"),
    @("CM7/Core/Src/stm32h7xx_hal_timebase_tim.c", "stm32h7xx_hal_timebase_tim.o", "C"),
    @("CM7/Core/Src/freertos.c", "freertos.o", "C"),
    @("CM7/Core/Src/main_user.c", "main_user.o", "C"),
    
    # TouchGFX App files
    @("CM7/TouchGFX/App/app_touchgfx.c", "app_touchgfx.o", "C"),
    
    # FreeRTOS files
    @("Middlewares/Third_Party/FreeRTOS/Source/tasks.c", "tasks.o", "C"),
    @("Middlewares/Third_Party/FreeRTOS/Source/queue.c", "queue.o", "C"),
    @("Middlewares/Third_Party/FreeRTOS/Source/list.c", "list.o", "C"),
    @("Middlewares/Third_Party/FreeRTOS/Source/timers.c", "timers.o", "C"),
    @("Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.c", "port.o", "C"),
    @("Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c", "heap_4.o", "C"),
    @("Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c", "cmsis_os2.o", "C"),
    
    # Essential HAL drivers
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c", "stm32h7xx_hal.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c", "stm32h7xx_hal_adc.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c", "stm32h7xx_hal_adc_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c", "stm32h7xx_hal_cortex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c", "stm32h7xx_hal_dma.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c", "stm32h7xx_hal_dma_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.c", "stm32h7xx_hal_dma2d.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c", "stm32h7xx_hal_gpio.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c", "stm32h7xx_hal_rcc.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c", "stm32h7xx_hal_rcc_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c", "stm32h7xx_hal_pwr.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c", "stm32h7xx_hal_pwr_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c", "stm32h7xx_hal_tim.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c", "stm32h7xx_hal_tim_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.c", "stm32h7xx_hal_ltdc.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.c", "stm32h7xx_hal_ltdc_ex.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.c", "stm32h7xx_hal_sdram.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.c", "stm32h7xx_hal_qspi.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_jpeg.c", "stm32h7xx_hal_jpeg.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c", "stm32h7xx_hal_mdma.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c", "stm32h7xx_hal_hsem.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.c", "stm32h7xx_hal_crc.o", "C"),
    @("Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.c", "stm32h7xx_ll_fmc.o", "C"),
    
    # BSP files
    @("Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery.c", "stm32h745i_discovery.o", "C"),
    @("Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_qspi.c", "stm32h745i_discovery_qspi.o", "C"),
    @("Drivers/BSP/STM32H745I-DISCO/stm32h745i_discovery_sdram.c", "stm32h745i_discovery_sdram.o", "C"),
    
    # TouchGFX C++ files
    @("CM7/TouchGFX/gui/src/model/Model.cpp", "Model.o", "C++"),
    @("CM7/TouchGFX/gui/src/screen1_screen/Screen1Presenter.cpp", "Screen1Presenter.o", "C++"),
    @("CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp", "Screen1View.o", "C++"),
    @("CM7/TouchGFX/target/TouchGFXHAL.cpp", "TouchGFXHAL.o", "C++"),
    @("CM7/TouchGFX/target/TouchGFXGPIO.cpp", "TouchGFXGPIO.o", "C++"),
    @("CM7/TouchGFX/target/STM32TouchController.cpp", "STM32TouchController.o", "C++"),
    @("CM7/TouchGFX/target/STM32H7Instrumentation.cpp", "STM32H7Instrumentation.o", "C++"),
    
    # TouchGFX generated files
    @("CM7/TouchGFX/generated/gui_generated/src/common/FrontendApplicationBase.cpp", "FrontendApplicationBase.o", "C++"),
    @("CM7/TouchGFX/generated/gui_generated/src/screen1_screen/Screen1ViewBase.cpp", "Screen1ViewBase.o", "C++")
)

Write-Host "`n=== Compiling All Source Files ===" -ForegroundColor Cyan

foreach ($file in $AllSourceFiles) {
    $SourceFile = $file[0]
    $OutputFile = $file[1]
    $FileType = $file[2]
    
    if ($FileType -eq "C" -or $FileType -eq "ASM") {
        if (Compile-SourceFile $GCC $CFLAGS $SourceFile $OutputFile $FileType) {
            $ObjectFiles += "$BUILD_DIR/$OutputFile"
            $SuccessCount++
        } else {
            $ErrorCount++
        }
    } elseif ($FileType -eq "C++") {
        if (Compile-SourceFile $GPP $CPPFLAGS $SourceFile $OutputFile $FileType) {
            $ObjectFiles += "$BUILD_DIR/$OutputFile"
            $SuccessCount++
        } else {
            $ErrorCount++
        }
    }
}

Write-Host "`n=== Compilation Summary ===" -ForegroundColor Cyan
Write-Host "Successful: $SuccessCount files" -ForegroundColor Green
Write-Host "Failed: $ErrorCount files" -ForegroundColor Red

if ($ErrorCount -gt 10) {
    Write-Host "Too many compilation errors!" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== Linking ===" -ForegroundColor Cyan
Write-Host "Linking executable..."

$LinkerScript = "gcc/STM32H745XIHX_FLASH_CM7.ld"
if (!(Test-Path $LinkerScript)) {
    Write-Host "Linker script not found: $LinkerScript" -ForegroundColor Red
    exit 1
}

Write-Host "Using linker script: $LinkerScript"

# Link
$link_args = $LDFLAGS + @("-T$LinkerScript") + $ObjectFiles + @("-o", "$BUILD_DIR/MyApplication_4.elf")
& $GCC @link_args

if ($LASTEXITCODE -eq 0) {
    Write-Host "SUCCESS: Linking completed" -ForegroundColor Green
} else {
    Write-Host "FAILED: Linking failed" -ForegroundColor Red
    exit 1
}

# Generate binary and hex files
Write-Host "`n=== Generating Binary Files ===" -ForegroundColor Cyan
& $OBJCOPY -O binary "$BUILD_DIR/MyApplication_4.elf" "$BUILD_DIR/MyApplication_4.bin"
& $OBJCOPY -O ihex "$BUILD_DIR/MyApplication_4.elf" "$BUILD_DIR/MyApplication_4.hex"

# Show size
Write-Host "`n=== Memory Usage ===" -ForegroundColor Cyan
& $SIZE "$BUILD_DIR/MyApplication_4.elf"

Write-Host "`n=== Flashing with ST-LINK ===" -ForegroundColor Cyan
Write-Host "Connecting to STM32H745I-DISCO..."

# Flash the firmware
$flash_args = @("-c", "port=SWD", "freq=4000", "-w", "$BUILD_DIR/MyApplication_4.hex", "-v", "-rst")
& $STLINK @flash_args

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nSUCCESS: Firmware flashed successfully!" -ForegroundColor Green
    Write-Host "ADC1 gauge control system is now running on STM32H745I-DISCO" -ForegroundColor Green
    Write-Host "The gauge should respond to voltage changes on PA1 (ADC1 Channel 17)" -ForegroundColor Cyan
    Write-Host "Voltage range: 0-3.3V maps to gauge range: 0-50" -ForegroundColor Cyan
} else {
    Write-Host "`nFAILED: Flash operation failed" -ForegroundColor Red
    Write-Host "Please check ST-LINK connection and try again" -ForegroundColor Yellow
}

Write-Host "`nBuild and flash process completed!" -ForegroundColor Cyan
