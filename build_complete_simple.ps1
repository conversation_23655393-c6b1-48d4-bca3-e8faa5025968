# 简化的TouchGFX完整项目编译脚本
Write-Host "=== STM32H745I TouchGFX Complete Build ===" -ForegroundColor Green

# 工具链路径
$TOOLCHAIN = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin"
$GCC = "$TOOLCHAIN\arm-none-eabi-gcc.exe"
$GPP = "$TOOLCHAIN\arm-none-eabi-g++.exe"
$OBJCOPY = "$TOOLCHAIN\arm-none-eabi-objcopy.exe"
$SIZE = "$TOOLCHAIN\arm-none-eabi-size.exe"

# 检查工具链是否存在
if (!(Test-Path $GCC)) {
    Write-Host "错误: 找不到GCC编译器: $GCC" -ForegroundColor Red
    exit 1
}

# 编译标志
$CFLAGS = "-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 --specs=nano.specs -Os -Wall -fdata-sections -ffunction-sections -g3 -std=gnu11"
$CPPFLAGS = "-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -DUSE_BPP=16 --specs=nano.specs -Os -Wall -fdata-sections -ffunction-sections -fno-exceptions -fno-rtti -g3 -std=c++17"

# 包含路径
$INCLUDES = "-ICM7/Core/Inc -ICM7/TouchGFX/App -ICM7/TouchGFX/target/generated -ICM7/TouchGFX/target -ICM7/TouchGFX/gui/include -ICM7/TouchGFX/generated/gui_generated/include -IDrivers/STM32H7xx_HAL_Driver/Inc -IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy -IDrivers/CMSIS/Device/ST/STM32H7xx/Include -IDrivers/CMSIS/Include -IDrivers/BSP/STM32H745I-DISCO -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -IMiddlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:\Users\<USER>\STMicroelectronics\TouchGFX\4.25.0\touchgfx\framework\include"

# 创建构建目录
$BUILD_DIR = "build_simple"
if (Test-Path $BUILD_DIR) { Remove-Item -Recurse -Force $BUILD_DIR }
New-Item -ItemType Directory -Path $BUILD_DIR | Out-Null

Write-Host "编译启动文件..." -ForegroundColor Yellow
$cmd = "$GCC $CFLAGS $INCLUDES -c gcc/startup_stm32h745xihx_cm7.s -o $BUILD_DIR/startup.o"
Invoke-Expression $cmd
if ($LASTEXITCODE -ne 0) { Write-Host "启动文件编译失败!" -ForegroundColor Red; exit 1 }

Write-Host "编译核心文件..." -ForegroundColor Yellow
$CoreFiles = @(
    "CM7/Core/Src/main.c",
    "CM7/Core/Src/stm32h7xx_it.c", 
    "CM7/Core/Src/stm32h7xx_hal_msp.c",
    "CM7/Core/Src/stm32h7xx_hal_timebase_tim.c",
    "CM7/Core/Src/freertos.c",
    "CM7/Core/Src/main_user.c"
)

foreach ($file in $CoreFiles) {
    $objname = "$BUILD_DIR/" + [System.IO.Path]::GetFileNameWithoutExtension($file) + ".o"
    Write-Host "  编译: $file"
    $cmd = "$GCC $CFLAGS $INCLUDES -c $file -o $objname"
    Invoke-Expression $cmd
    if ($LASTEXITCODE -ne 0) { Write-Host "编译失败: $file" -ForegroundColor Red; exit 1 }
}

Write-Host "编译HAL驱动..." -ForegroundColor Yellow
$HALFiles = @(
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma2d.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_ltdc_ex.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sdram.c",
    "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_fmc.c"
)

foreach ($file in $HALFiles) {
    $objname = "$BUILD_DIR/" + [System.IO.Path]::GetFileNameWithoutExtension($file) + ".o"
    Write-Host "  编译: $file"
    & $GCC $CFLAGS.Split(' ') $INCLUDES.Split(' ') -c $file -o $objname
    if ($LASTEXITCODE -ne 0) { Write-Host "编译失败: $file" -ForegroundColor Red; exit 1 }
}

Write-Host "编译TouchGFX应用..." -ForegroundColor Yellow
& $GCC $CFLAGS.Split(' ') $INCLUDES.Split(' ') -c "CM7/TouchGFX/App/app_touchgfx.c" -o "$BUILD_DIR/app_touchgfx.o"
if ($LASTEXITCODE -ne 0) { Write-Host "TouchGFX应用编译失败!" -ForegroundColor Red; exit 1 }

Write-Host "编译FreeRTOS..." -ForegroundColor Yellow
$FreeRTOSFiles = @(
    "Middlewares/Third_Party/FreeRTOS/Source/tasks.c",
    "Middlewares/Third_Party/FreeRTOS/Source/queue.c",
    "Middlewares/Third_Party/FreeRTOS/Source/list.c",
    "Middlewares/Third_Party/FreeRTOS/Source/timers.c",
    "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.c",
    "Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c",
    "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c"
)

foreach ($file in $FreeRTOSFiles) {
    $objname = "$BUILD_DIR/" + [System.IO.Path]::GetFileNameWithoutExtension($file) + ".o"
    Write-Host "  编译: $file"
    & $GCC $CFLAGS.Split(' ') $INCLUDES.Split(' ') -c $file -o $objname
    if ($LASTEXITCODE -ne 0) { Write-Host "编译失败: $file" -ForegroundColor Red; exit 1 }
}

Write-Host "编译TouchGFX C++文件..." -ForegroundColor Yellow
$TouchGFXCppFiles = @(
    "CM7/TouchGFX/gui/src/model/Model.cpp",
    "CM7/TouchGFX/gui/src/screen1_screen/Screen1Presenter.cpp",
    "CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp",
    "CM7/TouchGFX/target/TouchGFXHAL.cpp",
    "CM7/TouchGFX/target/TouchGFXGPIO.cpp",
    "CM7/TouchGFX/target/STM32TouchController.cpp",
    "CM7/TouchGFX/target/STM32H7Instrumentation.cpp"
)

foreach ($file in $TouchGFXCppFiles) {
    $objname = "$BUILD_DIR/" + [System.IO.Path]::GetFileNameWithoutExtension($file) + ".o"
    Write-Host "  编译: $file"
    & $GPP $CPPFLAGS.Split(' ') $INCLUDES.Split(' ') -c $file -o $objname
    if ($LASTEXITCODE -ne 0) { Write-Host "编译失败: $file" -ForegroundColor Red; exit 1 }
}

Write-Host "链接..." -ForegroundColor Yellow
$LDFLAGS = "-mcpu=cortex-m7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard --specs=nano.specs --specs=nosys.specs -Wl,--gc-sections -static --specs=nano.specs -Wl,--start-group -lc -lm -Wl,--end-group"

# 收集所有目标文件
$ObjectFiles = Get-ChildItem "$BUILD_DIR/*.o" | ForEach-Object { $_.FullName }

# 链接
& $GCC $LDFLAGS.Split(' ') "-Tgcc/STM32H745XIHX_FLASH_CM7.ld" $ObjectFiles -o "$BUILD_DIR/MyApplication_4.elf"

if ($LASTEXITCODE -eq 0) {
    Write-Host "链接成功!" -ForegroundColor Green
    
    # 生成HEX和BIN文件
    & $OBJCOPY -O ihex "$BUILD_DIR/MyApplication_4.elf" "$BUILD_DIR/MyApplication_4.hex"
    & $OBJCOPY -O binary "$BUILD_DIR/MyApplication_4.elf" "$BUILD_DIR/MyApplication_4.bin"
    
    # 显示大小
    & $SIZE "$BUILD_DIR/MyApplication_4.elf"
    
    Write-Host "编译完成! 输出文件:" -ForegroundColor Green
    Write-Host "  ELF: $BUILD_DIR/MyApplication_4.elf" -ForegroundColor White
    Write-Host "  HEX: $BUILD_DIR/MyApplication_4.hex" -ForegroundColor White
    Write-Host "  BIN: $BUILD_DIR/MyApplication_4.bin" -ForegroundColor White
} else {
    Write-Host "链接失败!" -ForegroundColor Red
    exit 1
}
